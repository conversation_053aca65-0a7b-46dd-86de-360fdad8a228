import React from 'react';
import { BsAlphabetUppercase } from 'react-icons/bs';
import { Link } from 'react-router-dom';

const TowerFloorTable = ({ floors}) => {


  return (
     <div className="overflow-x-auto overflow-y-auto max-h-[300px] scroll-smooth">
      <table className="min-w-[600px] table-auto border-collapse">
        <tbody>
          {floors
            .slice()
            .sort((a, b) => b.floor_no - a.floor_no)
            .map((floor) => (
              <tr key={floor.id} className="border-b">
                <td className="font-semibold text-center text-gray-500 bg-white sticky left-0 z-10">
                  Floor {floor.floor_no}
                </td>
                    {floor.units.map((unit) => {
                      const className = `bg-[${unit?.status_color}] ${
                        unit?.unit_status !== 'no_owner' ? 'text-white' : 'text-black'
                      }` || "bg-gray-100 text-gray-400";
                      
                      const tooltip = unit.unit_status !== 'no_owner' ? unit.unit_status : 'no owner';
                      // const tooltip = toCamelCase(tooltipText);
                      
                      return (
                        <td key={unit.id} className="border font-bold text-center p-0 m-0">
                          <Link to={`/unit-details/${unit.id}`}>
                            <div className={`py-3 ${className}`} title={tooltip}>
                              {unit.unit_name}
                            </div>
                          </Link>
                        </td>
                      );
                    })}
              </tr>
            ))}
        </tbody>
      </table>
    </div>
  );
};

export default TowerFloorTable; 