// import React, { useEffect, useMemo } from "react";
// import { Link, useLocation, useNavigate, useParams } from "react-router-dom";
// import ArrowHeading from "../../Components/HeadingComponent/ArrowHeading";
// import PageContainer from "../../Components/Ui/PageContainer";
// import { useDispatch, useSelector } from "react-redux";
// import ContentBox from "../../Components/Ui/ContentBox";
// import Row from "../../Components/Ui/Row";
// import MemberSummary from "../../Features/Members/MemberProfile/MemberSummary";
// import MemberDetails from "../../Features/Members/MemberProfile/MemberDetails";
// import Line from "../../Components/Ui/Line";
// import { fetchMemberById } from "../../redux/slices/api/memberApi";
// import LoadingAnimation from "../../Components/Loaders/LoadingAnimation";

// const MemberProfilePage = () => {
//   const { id } = useParams();
//   const dispatch = useDispatch();
//   const navigate = useNavigate();
//   const location = useLocation();

//   // use shallowEqual to avoid unnecessary re-renders when selectedMember hasn't changed
//   const selectedMember = useSelector((state) => state.member.selectedMember);

//   const memberId = useMemo(() => Number(id), [id]);
//   const handleGoBack = () => {
//     navigate(-1);

//   };

//   useEffect(() => {
//     if (!selectedMember || selectedMember.member?.id !== memberId) {
//       dispatch(fetchMemberById(memberId));
//     }
//   }, [selectedMember, memberId, dispatch]);

//   if (!selectedMember) {
//     return <LoadingAnimation />;
//   }

//   // console.log(selectedMember,"selectedMember abr")
//   return (
//     <PageContainer>
//       <div onClick={handleGoBack} style={{ cursor: "pointer" }}>
//         <ArrowHeading title="Profile" size="xl" color="text-black" />
//       </div>
//       <ContentBox className="max-w-content">
//         <Row>
//           <MemberSummary member={selectedMember.member} />
//           <MemberDetails selectedMember={selectedMember} />
//         </Row>
//       </ContentBox>
//     </PageContainer>
//   );
// };

// export default React.memo(MemberProfilePage);

import React, { useEffect } from "react";
import { Link, useLocation, useNavigate, useParams } from "react-router-dom";
import ArrowHeading from "../../Components/HeadingComponent/ArrowHeading";
import PageContainer from "../../Components/Ui/PageContainer";
import { useDispatch, useSelector } from "react-redux";
import ContentBox from "../../Components/Ui/ContentBox";
import Row from "../../Components/Ui/Row";
import MemberSummary from "../../Features/Members/MemberProfile/MemberSummary";
import MemberDetails from "../../Features/Members/MemberProfile/MemberDetails";
import Line from "../../Components/Ui/Line";
import { fetchMemberById } from "../../redux/slices/api/memberApi";
import LoadingAnimation from "../../Components/Loaders/LoadingAnimation";
import { setActiveTabs } from "../../redux/slices/memberSlice";

const MemberProfilePage = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  const selectedMember = useSelector((state) => state.member.selectedMember);
  const memberId = Number(id);

  useEffect(() => {
    dispatch(fetchMemberById(memberId));
  }, [dispatch, memberId, location.key]); // Add location.key to dependencies

  const handleGoBack = () => {
    const from = location.state?.from;
    if (from) {
      navigate(from);
    } else {
      navigate(-1);
    }
  };

  if (!selectedMember || selectedMember.member?.id !== memberId) {
    return <LoadingAnimation />;
  }

  return (
    <PageContainer>
      <div onClick={handleGoBack} style={{ cursor: "pointer" }} className="mb-4">
        <ArrowHeading title="Profile " size="2xl" color="text-black" />
      </div>

      <ContentBox className="max-w-content">
        <Row>
          <MemberSummary member={selectedMember.member} />
          <MemberDetails selectedMember={selectedMember} />
        </Row>
      </ContentBox>
    </PageContainer>
  );
};

export default React.memo(MemberProfilePage);
