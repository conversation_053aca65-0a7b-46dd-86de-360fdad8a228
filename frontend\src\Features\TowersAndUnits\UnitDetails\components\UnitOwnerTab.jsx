import React, { useEffect, useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { <PERSON>a<PERSON><PERSON>, FaPlus, FaEdit } from "react-icons/fa";
import Button from "../../../../Components/FormComponent/ButtonComponent/Button";
import { useDispatch, useSelector } from "react-redux";
import { fetchOwnerList } from "../../../../redux/slices/owner/ownerSlice";
import axios from "axios";
import userImage from "../../../../assets/user/user.png";
import { setActiveTabs } from "../../../../redux/slices/memberSlice";
import { checkPermission } from "utils/permissionUtils";
 

// Create an axios instance to use the base URL for images.
const api = axios.create({
  baseURL: import.meta.env.VITE_BASE_API
});

const UnitOwnerTab = ({ unitId }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  // Permission state
  const [hasPermission, setHasPermission] = useState(false);
  const [loadingPermission, setLoadingPermission] = useState(true);

  useEffect(() => {
    const fetchPermission = async () => {
   
      const permissionGranted = await checkPermission("org", 15);
      setHasPermission(permissionGranted);
      setLoadingPermission(false);
    };
    fetchPermission();
  }, []);

  const { ownerList, loading, error } = useSelector((state) => state.owner);

  useEffect(() => {
    if (unitId && hasPermission) {
      dispatch(fetchOwnerList(unitId));
    }
  }, [dispatch, unitId, hasPermission]);

  if (loadingPermission) return <div className="p-4">Loading...</div>;
  if (!hasPermission) {
    navigate("/not-authorized");
    return null;
  }

  if (loading) return <div className="p-4">Loading...</div>;
  if (error) return <div className="p-4 text-red-500">Error: {error}</div>;

  const owners = ownerList.owners || [];
  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-lg font-bold text-teal-600">Owner List</h2>
        <div className="flex gap-2 py-2">
          {owners.length > 0 ? (
            <Button
              size="small"
              className="flex items-center justify-between text-lg font-medium bg-primary text-white hover:bg-teal-500 transition-all duration-300 p-2 rounded"
              onClick={() => navigate(`/unit/${unitId}/change-owner`)}
            >
              Change Ownership
            </Button>
          ) : (
            <Button
              size="small"
              className="flex items-center justify-between text-lg font-medium bg-primary text-white hover:bg-teal-500 transition-all duration-300 p-2 rounded"
              onClick={() => navigate(`/unit/${unitId}/add-owner`)}
            >
              <FaPlus className="mr-2" />
              Add Owner
            </Button>
          )}
        </div>
      </div>

      <table className="w-full text-sm text-left rtl:text-right table-auto">
        <thead className="bg-subprimary shadow-lg">
          <tr>
            <th className="w-[200px] px-2 py-3 font-semibold text-teal-800 text-left">
              Name
            </th>
            <th className="w-[150px] px-2 py-3 font-semibold text-teal-800 text-left">
              Contact
            </th>
            <th className="w-[220px] px-2 py-3 font-semibold text-teal-800 text-left">
              Email
            </th>
            <th className="w-[120px] px-2 py-3 font-semibold text-teal-800 text-left">
              Ownership
            </th>
            <th className="w-[100px] px-2 py-3 font-semibold text-teal-800 text-center">
              Action
            </th>
          </tr>
        </thead>
        <tbody>
          {owners.length === 0 ? (
            <tr>
              <td colSpan="5" className="px-2 py-4 text-center text-gray-500">
                No results found.
              </td>
            </tr>
          ) : (
            owners.map((owner, index) => {
              const member = owner.member;
              return (
                <tr key={index} className="bg-white border-b hover:bg-gray-50">
                  <td className="px-2 py-3 font-medium">
                    <div className="flex items-center gap-2">
                      <img
                        src={
                          member.photo_low_quality
                            ? member.photo_low_quality.startsWith('http')
                              ? member.photo_low_quality
                              : `${api.defaults.baseURL}${member.photo_low_quality}`
                            : userImage
                        }
                        className="w-8 h-8 rounded-full"
                        alt="user"
                      />
                      <span>{member.full_name}</span>
                    </div>
                  </td>
                  <td className="px-2 py-3 text-sm">
                    {member.general_contact}
                  </td>
                  <td className="px-2 py-3 text-sm">{member.general_email}</td>
                  <td className="px-2 py-3 text-sm">
                    {owner.ownership_percentage} %
                  </td>
                  <td className="px-2 py-3">
                    <div className="flex justify-center items-center gap-4">
                      <Link
                        to={{
                          pathname: `/member-profile/${member.id}`,
                          state: { from: location.pathname }
                        }}
                        onClick={() => dispatch(setActiveTabs(1))}
                        className="p-2 rounded-full hover:bg-teal-50 transition-all duration-300"
                        title="View Profile"
                      >
                        <FaEye className="w-5 h-5 text-teal-600 hover:text-teal-700 transition-colors" />
                      </Link>
                      <Link
                        to={{
                          pathname: `/unit/${unitId}/edit-owner/${owner.id}`,
                          state: { from: location.pathname }
                        }}
                        onClick={() => dispatch(setActiveTabs(1))}
                        className="p-2 rounded-full hover:bg-teal-50 transition-all duration-300"
                        title="Edit Owner"
                      >
                        <FaEdit className="w-5 h-5 text-teal-600 hover:text-teal-700 transition-colors" />
                      </Link>
                    </div>
                  </td>
                </tr>
              );
            })
          )}
        </tbody>
      </table>
    </div>
  );
};

export default UnitOwnerTab;
