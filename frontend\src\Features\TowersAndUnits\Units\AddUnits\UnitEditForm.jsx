import React from "react";
import { FaAddressBook } from "react-icons/fa";
import ErrorMessage from "../../../../Components/MessageBox/ErrorMessage";
import MultipleImageDropzoneUnitEdit from "../components/MultipleImageDropzoneUnitEdit";

const UnitEditForm = ({
  selectedUnitDetails,
  onSubmit,
  handleUpload,
  contactType,
  setContactType,
  showModal,
  setShowModal,
  memberContact,
  handleContactSelect,
  uploadedImages,
  register,
  handleSubmit,
  setValue,
  errors,
  isDirty
}) => {
  const phoneValidation = {
    validate: (value) =>
      !value ||
      /^(018|019|013|017|015|016|014)\d{8}$/.test(value) ||
      "Invalid Bangladeshi phone number"
  };

  const emailValidation = {
    validate: (value) =>
      !value ||
      /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/.test(value) ||
      "Enter a valid email address"
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="p-3" method="POST">
      <h3 className="text-base text-primary font-bold ">
        General Information
      </h3>
      <div className="grid grid-cols-2 gap-3 lg:w-[687px] mb-3">
        <div className="login-field">
          <div className="mb-2">
            <label className="block ">Area/ sq. ft.</label>
          </div>
          <input
            type="number"
            {...register("area", {
              required: "Area is required",
              min: { value: 1, message: "Area must be greater than 0" }
            })}
            className="border rounded p-2 w-full login-field-input"
            placeholder="Area"
          />

          {errors.area && <ErrorMessage message={errors.area.message} />}
        </div>

        <div className="login-field">
          <div className="mb-2">
            {" "}
            <label className="block ">Number of Rooms</label>
          </div>
          <input
            type="number"
            {...register("number_of_rooms", {
              required: "Number of rooms is required",
              min: {
                value: 1,
                message: "Number of rooms must be greater than 0"
              }
            })}
            className="border rounded p-2 w-full login-field-input"
            placeholder="Number of Rooms"
          />

          {errors.number_of_rooms && (
            <ErrorMessage message={errors.number_of_rooms.message} />
          )}
        </div>

        <div className="login-field">
          <div className="mb-2">
            {" "}
            <label className="block ">Number of Bathrooms</label>
          </div>
          <input
            type="number"
            {...register("number_of_bathrooms", {
              required: "Number of bathrooms is required",
              min: {
                value: 0,
                message: "Number of bathrooms must be at least 0"
              }
            })}
            className="border rounded p-2 w-full login-field-input"
            placeholder="Number of Bathrooms"
          />

          {errors.number_of_bathrooms && (
            <ErrorMessage message={errors.number_of_bathrooms.message} />
          )}
        </div>

        <div className="login-field">
          <div className="mb-2">
            <label className="block ">Number of Balconies</label>
          </div>
          <input
            type="number"
            {...register("number_of_balconies", {
              required: "Number of balconies is required",
              min: {
                value: 0,
                message: "Number of balconies must be at least 0"
              }
            })}
            className="border rounded p-2 w-full login-field-input"
            placeholder="Number of Balconies"
          />

          {errors.number_of_balconies && (
            <ErrorMessage message={errors.number_of_balconies.message} />
          )}
        </div>
      </div>

      {/* Primary Contact */}
      <div className="flex justify-between items-center mt-3">
        <h3 className="text-base text-primary font-bold pb-4 mt-4">
          Primary Contact
        </h3>
        <button
          type="button"
          onClick={() => {
            setContactType("primary");
            setShowModal(true);
          }}
          className="bg-primary text-white rounded text-sm py-2 px-4 m-2 flex items-center"
        >
          <FaAddressBook className="mr-2" />
          Primary Contact
        </button>
      </div>
      <div className="grid grid-cols-2 gap-3 lg:w-[687px]">
        <div className="login-field">
          <div className="mb-2">
            {" "}
            <label className="block ">Name</label>
          </div>
          <input
            type="text"
            {...register("primary_name")}
            className="border rounded p-2 w-full login-field-input"
            placeholder="Name"
          />
        </div>
        <div className="login-field">
          <div className="mb-2">
            <label className="block ">Phone Number</label>
          </div>
          <input
            type="text"
            {...register("primary_number", phoneValidation)}
            className="border rounded p-2 w-full login-field-input"
            placeholder="Phone Number"
          />

          {errors.primary_number && (
            <ErrorMessage message={errors.primary_number.message} />
          )}
        </div>

        <div className="login-field">
          <div className="mb-2">
            {" "}
            <label className="block ">Email</label>
          </div>
          <input
            type="email"
            {...register("primary_email", emailValidation)}
            className="border rounded p-2 w-full login-field-input"
            placeholder="Email"
          />

          {errors.primary_email && (
            <ErrorMessage message={errors.primary_email.message} />
          )}
        </div>
        <div className="login-field">
          <div className="mb-2">
            <label className="block ">Relationship</label>
          </div>
          <input
            type="text"
            {...register("primary_relationship")}
            className="border rounded p-2 w-full login-field-input"
            placeholder="Relationship"
          />
        </div>
      </div>

      {/* Secondary Contact */}
      <div className="flex justify-between items-center mt-3">
        <h3 className="text-base text-primary font-bold pb-4 mt-4">
          Secondary Contact
        </h3>
        <button
          type="button"
          onClick={() => {
            setContactType("secondary");
            setShowModal(true);
          }}
          className="bg-primary text-white rounded text-sm py-2 px-4 m-2 flex items-center"
        >
          <FaAddressBook className="mr-2" />
          Secondary Contact
        </button>
      </div>
      <div className="grid grid-cols-2 gap-3 lg:w-[687px]">
        <div className="login-field">
          <div className="mb-2">
            {" "}
            <label className="block ">Name</label>
          </div>
          <input
            type="text"
            {...register("secondary_name")}
            className="border rounded p-2 w-full login-field-input"
            placeholder="Name"
          />
        </div>
        <div className="login-field">
          <div className="mb-2">
            {" "}
            <label className="block ">Phone Number</label>
          </div>
          <input
            type="text"
            {...register("secondary_number", phoneValidation)}
            className="border rounded p-2 w-full login-field-input"
            placeholder="Phone Number"
          />

          {errors.secondary_number && (
            <ErrorMessage message={errors.secondary_number.message} />
          )}
        </div>

        <div className="login-field">
          <div className="mb-2">
            {" "}
            <label className="block ">Email</label>
          </div>
          <input
            type="email"
            {...register("secondary_email", emailValidation)}
            className="border rounded p-2 w-full login-field-input"
            placeholder="Email"
          />

          {errors.secondary_email && (
            <ErrorMessage message={errors.secondary_email.message} />
          )}
        </div>
        <div className="login-field">
          <div className="mb-2">
            {" "}
            <label className="block ">Relationship</label>
          </div>
          <input
            type="text"
            {...register("secondary_relationship")}
            className="border rounded p-2 w-full login-field-input"
            placeholder="Relationship"
          />
        </div>
      </div>

      {/* Emergency Contact */}
        <h3 className="text-base text-primary font-bold pb-4 mt-4">
          Emergency Contact
        </h3>
       
      <div className="grid grid-cols-2 gap-3 lg:w-[687px]">
        <div className="login-field">
          <div className="mb-2">
            {" "}
            <label className="block ">Name</label>
          </div>
          <input
            type="text"
            {...register("emergency_name")}
            className="border rounded p-2 w-full login-field-input"
            placeholder="Name"
          />
        </div>
        <div className="login-field">
          <div className="mb-2">
            <label className="block ">Phone Number</label>
          </div>
          <input
            type="text"
            {...register("emergency_number", phoneValidation)}
            className="border rounded p-2 w-full login-field-input"
            placeholder="Phone Number"
          />

          {errors.emergency_number && (
            <ErrorMessage message={errors.emergency_number.message} />
          )}
        </div>

        <div className="login-field">
          <div className="mb-2">
            {" "}
            <label className="block ">Email</label>
          </div>
          <input
            type="email"
            {...register("emergency_email", emailValidation)}
            className="border rounded p-2 w-full login-field-input"
            placeholder="Emergency Email"
          />

          {errors.emergency_email && (
            <ErrorMessage message={errors.emergency_email.message} />
          )}
        </div>
        <div className="login-field">
          <div className="mb-2">
            <label className="block ">Relationship</label>
          </div>
          <input
            type="text"
            {...register("emergency_relationship")}
            className="border rounded p-2 w-full login-field-input"
            placeholder="Relationship"
          />
        </div>

        <div className="flex justify-left gap-2 pb-5 col-span-2">
          <MultipleImageDropzoneUnitEdit
            onUpload={handleUpload}
            initialFiles={selectedUnitDetails?.docs || []}
          />
        </div>
      </div>

      <div className="lg:w-[687px] ">
        <button
          type="submit"
          className={`px-4 py-2  font-semibold rounded transition-all duration-200 w-full ${
            isDirty
              ? "bg-primary text-white"
              : "bg-white cursor-not-allowed border border-primary text-primary"
          }`}
          disabled={!isDirty}
        >
          Save
        </button>
      </div>
    </form>
  );
};

export default UnitEditForm;
