@tailwind base;
@tailwind components;
@tailwind utilities;

/* @import url('https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700&display=swap');

:root {
  --tw-font-sans: 'Lato', sans-serif; 
} */
body {
  overflow-x: hidden; 
  overflow-y: hidden; 
  width: 100%;
}
.member-profile-image11 {
  width: 304px !important;
  height: 250px !important;
}
.member_doc {
  height: 50px !important;
  width: 100px !important;
}
.member_doc_defult {
  height: 50px !important;
}

@layer base {
  body {
    @apply font-sans;
  }

  tr {
    min-height: 44px;
  }
  td {
    @apply py-3; /* Ensures spacing within cells */
  }
}



/* Hide the current month and year heading */
.react-datepicker__current-month {
 padding-top: 10px;
 margin-bottom: 10px !important;
}

