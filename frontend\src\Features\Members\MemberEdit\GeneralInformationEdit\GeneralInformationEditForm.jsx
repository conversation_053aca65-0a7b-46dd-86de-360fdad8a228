// src/your/path/GeneralInformationEditForm.jsx
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link, useNavigate, useParams } from "react-router-dom";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { Div } from "../../../../Components/Ui/Div";
import SideSectionEdit from "./SideSectionEdit";
import MainSectionEdit from "./MainSectionEdit";
import ArrowHeading from "../../../../Components/HeadingComponent/ArrowHeading";
import Heading from "../../../../Components/HeadingComponent/Heading";
import NidSectionEdit from "./NidSectionEdit";
import SubmitButton from "Components/FormComponent/ButtonComponent/SubmitButton";

import {
  fetchMemberById,
  memberUpdate
} from "../../../../redux/slices/api/memberApi";
import {
  setMessage,
  setError,
  clearMessage
} from "../../../../redux/slices/memberSlice";
import MessageBox from "../../../../Components/MessageBox/MessageBox";
import LoadingAnimation from "../../../../Components/Loaders/LoadingAnimation";
import { BiArrowBack } from "react-icons/bi";
import { setActiveTabs } from "../../../../redux/slices/companySlice";

const allowedTypes = ["image/png", "image/jpeg", "image/jpg"];
const errorMessage = "Only JPG, JPEG, PNG allowed.";

const contactRegex = /^(018|019|013|017|015|016|014)\d{8}$/;
const validationSchema = Yup.object({
  full_name: Yup.string().required("Full name is required"),
  general_email: Yup.string()
    .email("Invalid email")
    .required("Email is required"),
  general_contact: Yup.string()
    .matches(contactRegex, "Invalid contact format")
    .required("Contact number is required"),
  nid_number: Yup.string()
    .test(
      "is-valid-nid",
      "NID Number must be 10, 13, or 17 digits",
      (value) => {
        if (!value) return true;
        return /^(?:\d{10}|\d{13}|\d{17})$/.test(value);
      }
    )
    .notRequired()
});

const GeneralInformationEditForm = () => {
  const [showMessage, setShowMessage] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isFormChanged, setIsFormChanged] = useState(false);
  const navigate = useNavigate();
  const { id } = useParams();


  const dispatch = useDispatch();
  const { selectedMember, message, error } = useSelector(
    (state) => state.member
  );

  const [fileErrors, setFileErrors] = useState({
    photo: "",
    nid_front: "",
    nid_back: ""
  });

  const [formData, setFormData] = useState({
    full_name: "",
    general_email: "",
    general_contact: "",
    nid_number: "",
    permanent_address: "",
    present_address: "",
    date_of_birth: "",
    occupation: "",
    gender: "",
    marital_status: "",
    religion: "",
    about_us: "",
    facebook_profile: "",
    linkedin_profile: "",
    photo: "",
    nid_front: "",
    nid_back: ""
  });

  useEffect(() => {
    dispatch(fetchMemberById(id));
  }, [dispatch, id]);

  useEffect(() => {
    if (selectedMember?.member) {
      setFormData({ ...selectedMember?.member });
    }
  }, [selectedMember?.member]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    setIsFormChanged(true);
  };

  const handleFileChange = (e, fieldName) => {
    const file = e.target.files[0];
    setFileErrors((prev) => ({ ...prev, [fieldName]: "" }));
    if (file) {
      if (!allowedTypes.includes(file.type)) {
        setFileErrors((prev) => ({ ...prev, [fieldName]: errorMessage }));
        return;
      }
      setFormData((prev) => ({ ...prev, [fieldName]: file }));
      setIsFormChanged(true);
    }
  };

  const removeFile = (fieldName) => {
    setFormData((prev) => ({ ...prev, [fieldName]: "" }));
    setIsFormChanged(true);
  };

  const handleSubmit = async (values) => {
    const body = new FormData();
    Object.keys(values).forEach((key) => {
      if (key === "photo_low_quality") return;
      const val = values[key];
      if (["photo", "nid_front", "nid_back"].includes(key)) {
        if (val instanceof File || val instanceof Blob) {
          body.append(key, val);
        } else if (val === "") {
          body.append(`${key}_removed`, "Removed");
        }
      } else {
        body.append(key, val || "");
      }
    });

    setLoading(true);
    try {
      const resultAction = await dispatch(memberUpdate({ id, formData: body }));
      if (memberUpdate.fulfilled.match(resultAction)) {
        dispatch(setMessage("Member updated successfully!"));
        setShowMessage(true);
        setIsFormChanged(false);
      } else {
        // Extract server validation errors if available
        let errorMsg = resultAction.error.message || "Failed to update member.";
        const payload = resultAction.payload;
        if (payload && typeof payload === "object") {
          errorMsg = Object.values(payload).flat().join(", ");
        }
        dispatch(setError(errorMsg));
        setShowMessage(true);
      }
    } catch (err) {
      dispatch(setError(err.message));
      setShowMessage(true);
    } finally {
      setLoading(false);
    }
  };


  console.log("selectedMember", selectedMember?.member)
  return (
    <Div className="h-full py-6">
      <Div className="container">
        {showMessage && (
          <MessageBox
            message={message}
            error={error}
            clearMessage={() => {
              dispatch(clearMessage());
              setShowMessage(false);
            }}
            onOk={() => {

              dispatch(clearMessage());
              setShowMessage(false);
              if (!error) {
                dispatch(setActiveTabs(1));
                navigate(-1);
              }
            }}
          />
        )}

        <Div className="md:flex justify-between ">
          <div>
            <button
              type="button"
              onClick={() => navigate(-1)}
              className="inline-flex items-center cursor-pointer text-xl mb-4"
            >
              <BiArrowBack className="mr-2" />
              Edit Personal Information
            </button>
            <Formik
              initialValues={formData}
              validationSchema={validationSchema}
              onSubmit={handleSubmit}
              enableReinitialize
            >
              {({ errors, touched }) => (
                <Form>
                  {loading && (
                    <div className="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 z-50">
                      <LoadingAnimation />
                    </div>
                  )}
                  <Div className="flex">
                    <SideSectionEdit
                      formData={formData}
                      handleChange={handleChange}
                      handleFileChange={handleFileChange}
                      removeFile={removeFile}
                      fileErrors={fileErrors}
                    />
                    <Div className="bg-white border-l p-5 rounded-tr-xl rounded-br-xl shadow-sm lg:w-[787px]">
                      <Heading
                        title="General Information"
                        size="lg"
                        color="text-black"
                      />
                      <MainSectionEdit
                        formData={formData}
                        handleChange={handleChange}
                        errors={errors}
                        touched={touched}
                      />
                      <NidSectionEdit
                        formData={formData}
                        handleChange={handleChange}
                        handleFileChange={handleFileChange}
                        removeFile={removeFile}
                        fileErrors={fileErrors}
                      />
                      <SubmitButton
                        text="Update"
                        width="full"
                        disabled={!isFormChanged}
                      />
                    </Div>
                  </Div>
                </Form>
              )}
            </Formik>
          </div>
        </Div>
      </Div>
    </Div>
  );
};

export default GeneralInformationEditForm;
