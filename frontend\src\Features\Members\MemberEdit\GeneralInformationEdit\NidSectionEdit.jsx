import React from "react";
import { RxCross2 } from "react-icons/rx";
import { Div } from "../../../../Components/Ui/Div";
import SingleImageUpload from "../../../../utils/SingleImageUpload";
// import nid from '../../../../../public/nid.png'
import front from "../../../../../src/assets/nid/front_nid.png";
import back from "../../../../../src/assets/nid/back_nid.png";
import ErrorMessage from "../../../../Components/MessageBox/ErrorMessage";

const NidSectionEdit = ({ formData, handleChange, handleFileChange, removeFile,fileErrors }) => {
  return (
    <div>
      <Div className="flex justify-between gap-3 py-2">
        <Div className="flex justify-center gap-2 pb-5 lg:w-[687px]">
          {/* NID Front */}
          <Div className="profile-picture flex flex-col items-center space-4 px-5 border border-dashed shadow-lg">
          {fileErrors.nid_front  && <ErrorMessage message={fileErrors.nid_front} />}

            <Div className="my-[20px] flex justify-center items-center">

              <Div className="relative py-1">
                <SingleImageUpload
                  file={formData.nid_front}
                  altImg={front}
                  customClass="member_doc object-cover"
                />
                {formData.nid_front && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      removeFile("nid_front");
                    }}
                    className="absolute top-0 right-0 p-1 rounded-full bg-primary text-white"
                  >
                    <RxCross2 />
                  </button>
                )}
              </Div>
            </Div>

            <Div className="my-[10px]">
              <label
                htmlFor="nid_front"
                className="cursor-pointer bg-[#3C9D9B] text-white py-2 px-4 rounded w-full"
              >
                {formData.nid_front ? "Change NID Front" : "Upload NID Front"}
              </label>
              <input
                id="nid_front"
                type="file"
                accept="image/*"
                className="hidden"
                onChange={(e) => handleFileChange(e, "nid_front")}
              />
            </Div>
          </Div>
          {/* {fileErrors.nid_front && <p className="text-red-500">{fileErrors.nid_front}</p>} */}


          {/* NID Back */}
          <Div className="profile-picture flex flex-col items-center space-4 px-5 border border-dashed ">
          {fileErrors.nid_back  && <ErrorMessage message={fileErrors.nid_back} />}

            <Div className="my-[20px] flex justify-center items-center">

              <Div className="relative py-1">
                <SingleImageUpload
                  file={formData.nid_back}
                  altImg={back}
                  customClass="member_doc object-cover"
                />
                {formData.nid_back && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      removeFile("nid_back");
                    }}
                    className="absolute top-0 right-0 p-1 rounded-full bg-primary text-white"
                  >
                    <RxCross2 />
                  </button>
                )}
              </Div>
            </Div>

            <Div className="my-[10px]">
              <label
                htmlFor="nid_back"
                className="cursor-pointer bg-[#3C9D9B] text-white py-2 px-4 rounded w-full"
              >
                {formData.nid_back ? "Change NID Back" : "Upload NID Back"}
              </label>
              <input
                id="nid_back"
                type="file"
                accept="image/*"
                className="hidden"
                onChange={(e) => handleFileChange(e, "nid_back")}
              />
            </Div>
            {/* {fileErrors.nid_back && <p className="text-red-500">{fileErrors.nid_back}</p>} */}


          </Div>
        </Div>
      </Div>
    </div>
  );
};

export default NidSectionEdit;
