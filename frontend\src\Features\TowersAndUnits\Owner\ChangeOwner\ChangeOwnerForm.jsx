import React, { useEffect, useRef, useState } from "react";
import { useForm, useField<PERSON><PERSON><PERSON>, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { FaMinus, FaPlus } from "react-icons/fa";

import {
  createOwner,
  updateOwner,
  deleteOwner,
  fetchOwnerList,
  clearCreatedMember,
  clearMessage
} from "../../../../redux/slices/owner/ownerSlice";

import {
  clearCreatedCompany,
  clearMessage as clearCompanyMessage
} from "../../../../redux/slices/companySlice";

import {
  ownerValidationSchema,
  getOrdinal,
  formatDate
} from "../utils/ownerUtils";

import { Paragraph } from "../../../../Components/Ui/Paragraph";
import NumberInputComponent from "../../../../Components/FormComponent/NumberInputComponent";
import FileDropzone from "../Components/FileDropzone";
import AddMemberForm from "../Components/Modals/AddMemberForm";
import AddCompany from "../AddCompany/AddCompany";
import MessageBox from "../../../../Components/MessageBox/MessageBox";
import MemberSearchAutocomplete from "../../../../Components/MemberSearchAutocomplete/MemberSearchAutocomplete";
import { fetchMemberById } from "../../../../redux/slices/api/memberApi";
import { setActiveTabs } from "../../../../redux/slices/memberSlice";

const ChangeOwnerForm = ({ unitId }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const ownerState = useSelector((state) => state.owner);

  const [searchTerms, setSearchTerms] = useState({});
  const [isMemberModalOpen, setIsMemberModalOpen] = useState(false);
  const [showCompanyModal, setShowCompanyModal] = useState(false);
  const [docsToDelete, setDocsToDelete] = useState({});
  const changeCompanyData = useSelector((state) => state.company.company_data);
  const [localError, setLocalError] = useState(null);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [indexToDelete, setIndexToDelete] = useState(null);
  const [hasFileChanges, setHasFileChanges] = useState(false);
  const [initialValues, setInitialValues] = useState(null);
  const [transferValidationError, setTransferValidationError] = useState(null);
  const [transferredPercentages, setTransferredPercentages] = useState({});
  const [originalPercentages, setOriginalPercentages] = useState({});
  const [hasTransfers, setHasTransfers] = useState(false);

  const ownerRefs = useRef([]);

  const {
    control,
    register,
    handleSubmit,
    setValue,
    setError,
    clearErrors,
    watch,
    formState: { errors, isDirty, dirtyFields }
  } = useForm({
    resolver: yupResolver(ownerValidationSchema),
    defaultValues: {
      owners: [],
      document: [],
      docLinks: []
    }
  });

  const { fields, append, remove, replace } = useFieldArray({
    control,
    name: "owners"
  });

  // Memoize the owners watch to prevent unnecessary re-renders
  const owners = React.useMemo(() => watch("owners"), [watch]);

  // Memoize the file changes effect
  useEffect(() => {
    const subscription = watch((value, { name, type }) => {
      if (name?.includes("document") || name?.includes("docLinks")) {
        setHasFileChanges(true);
      }
    });
    return () => subscription.unsubscribe();
  }, [watch]);

  // Memoize the search terms update effect
  useEffect(() => {
    if (!owners.length) return;

    const newTerms = { ...searchTerms };
    let hasChanges = false;

    owners.forEach((owner, idx) => {
      // Update owner name
      if (owner.memberId) {
        const member = ownerState.ownerList?.owners?.find(
          (o) => o.member.id === owner.memberId
        )?.member;
        if (member && newTerms[idx] !== member.full_name) {
          newTerms[idx] = member.full_name;
          hasChanges = true;
        }
      }

      // Update transfer from name
      if (owner.ownershipTransferFromId) {
        const transferFrom = ownerState.ownerList?.owners?.find(
          (o) => o.member.id === owner.ownershipTransferFromId
        )?.member;
        if (transferFrom && newTerms[`from_${idx}`] !== transferFrom.full_name) {
          newTerms[`from_${idx}`] = transferFrom.full_name;
          hasChanges = true;
        }
      }
    });

    if (hasChanges) {
      setSearchTerms(newTerms);
    }
  }, [owners, ownerState.ownerList, searchTerms]);

  // Add this effect to handle ownership transfer changes
  useEffect(() => {
    const subscription = watch((value, { name }) => {
      if (name?.includes('ownershipTransferFromId')) {
        const index = parseInt(name.split('.')[1]);
        const transferFromId = watch(`owners.${index}.ownershipTransferFromId`);

        if (transferFromId) {
          const transferFrom = ownerState.ownerList?.owners?.find(
            (o) => o.member.id === transferFromId
          )?.member;

          if (transferFrom) {
            setSearchTerms(prev => ({
              ...prev,
              [`from_${index}`]: transferFrom.full_name
            }));
          }
        } else {
          // Clear the transfer from name if no transfer from is selected
          setSearchTerms(prev => ({
            ...prev,
            [`from_${index}`]: ""
          }));
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [watch, ownerState.ownerList]);

  // Memoize the owner list transformation effect
  useEffect(() => {
    if (!ownerState.ownerList?.owners) return;

    let owners = [...(ownerState.ownerList.owners || [])];

    if (owners.length > 0) {
      const transformed = owners.map((owner) => ({
        id: owner.id,
        memberId: owner.member.id,
        ownershipPercentage: owner.ownership_percentage,
        dateofOwnership: owner.date_of_ownership
          ? new Date(owner.date_of_ownership).toISOString().split("T")[0]
          : "",
        document: [],
        docLinks: owner.docs?.map((doc) => ({
          id: doc.id,
          url: doc.url
        })) || [],
        ownershipTransferFromId: owner.ownership_transfer_from?.id || "",
        isExisting: true
      }));

      replace(transformed);

      const initialTerms = {};
      owners.forEach((owner, idx) => {
        initialTerms[idx] = owner.member.full_name;
        initialTerms[`from_${idx}`] =
          owner.ownership_transfer_from?.full_name || "";
      });
      setSearchTerms(initialTerms);
    }
  }, [ownerState.ownerList, replace]);

  // Add this effect to handle company data
  useEffect(() => {
    if (
      changeCompanyData?.data?.member &&
      changeCompanyData?.data?.company_name &&
      !fields.some((field) => field.memberId === changeCompanyData.data.member)
    ) {
      // Find an empty slot or create a new one
      const emptyIndex = fields.findIndex(
        (_, index) => !watch(`owners.${index}.memberId`)
      );

      if (emptyIndex === -1) {
        // Only append if there's no empty slot
        const newOwner = {
          id: changeCompanyData.data.member,

          memberId: changeCompanyData.data.member,
          ownershipPercentage: "",
          dateofOwnership: "",
          document: [],
          docLinks: [],
          ownershipTransferFromId: "",
          isExisting: false
        };

        append(newOwner);
        setSearchTerms((prev) => ({
          ...prev,
          [fields.length]: changeCompanyData.data.company_name
        }));
      } else {
        // Use the existing empty slot
        setValue(
          `owners.${emptyIndex}.memberId`,
          changeCompanyData.data.member
        );
        setValue(`owners.${emptyIndex}.isExisting`, false);
        setSearchTerms((prev) => ({
          ...prev,
          [emptyIndex]: changeCompanyData.data.company_name
        }));
      }

      setShowCompanyModal(false);
      dispatch(clearCreatedCompany());
      dispatch(clearCompanyMessage());
    }
  }, [changeCompanyData, append, fields, watch, setValue]);

  // Add effect to sync form with Redux state
  useEffect(() => {
    if (!ownerState.ownerList?.owners) {
      // If Redux state is cleared, reset the form
      replace([]);
      setSearchTerms({});
    }
  }, [ownerState.ownerList, replace]);

  // Add effect to store original percentages when owners are loaded
  useEffect(() => {
    if (ownerState.ownerList?.owners) {
      const initialPercentages = {};
      ownerState.ownerList.owners.forEach(owner => {
        initialPercentages[owner.member.id] = owner.ownership_percentage;
      });
      setOriginalPercentages(initialPercentages);
    }
  }, [ownerState.ownerList]);

  // Add this effect to check if there are any transfers
  useEffect(() => {
    const hasAnyTransfers = fields.some((_, index) =>
      !watch(`owners.${index}.isExisting`) &&
      watch(`owners.${index}.ownershipTransferFromId`)
    );
    setHasTransfers(hasAnyTransfers);
  }, [watch('owners')]);

  const handleRemoveOwner = (index) => {
    const ownerId = watch(`owners.${index}.id`);
    const isExistingOwner = watch(`owners.${index}.isExisting`);

    // Count existing owners
    const existingOwnersCount = fields.filter((_, idx) =>
      watch(`owners.${idx}.isExisting`)
    ).length;

    // Prevent removal if this is the last existing owner
    if (isExistingOwner && existingOwnersCount <= 1) {
      setLocalError(
        "Cannot remove the last owner. At least one owner must remain."
      );
      return;
    }

    // For new owners (including company owners), remove immediately
    if (!isExistingOwner) {
      // Remove the owner from the form array
      remove(index);

      // Update search terms after removal
      const newTerms = {};
      fields.forEach((_, idx) => {
        if (idx !== index) {
          const adjustedIdx = idx > index ? idx - 1 : idx;
          newTerms[adjustedIdx] = searchTerms[idx] || "";
          newTerms[`from_${adjustedIdx}`] = searchTerms[`from_${idx}`] || "";
        }
      });
      setSearchTerms(newTerms);

      // Update ownerRefs
      ownerRefs.current = ownerRefs.current.filter((_, idx) => idx !== index);

      // Force a re-render of the form by updating the entire owners array
      const updatedOwners = fields.filter((_, idx) => idx !== index);
      setValue("owners", updatedOwners);
    } else {
      // Ask confirmation only for existing owners
      setIndexToDelete(index);
      setShowConfirmation(true);
    }
  };

  // const handleConfirmDelete = async () => {
  //   if (indexToDelete !== null) {
  //     const ownerId = watch(`owners.${indexToDelete}.id`);
  //     try {
  //       await dispatch(deleteOwner(ownerId)).unwrap();
  //       remove(indexToDelete);

  //       // Update search terms after removal
  //       setSearchTerms((prev) => {
  //         const newTerms = {};
  //         // Rebuild search terms for remaining owners
  //         fields.forEach((_, idx) => {
  //           if (idx !== indexToDelete) {
  //             const adjustedIdx = idx > indexToDelete ? idx - 1 : idx;
  //             newTerms[adjustedIdx] = prev[idx] || "";
  //             newTerms[`from_${adjustedIdx}`] = prev[`from_${idx}`] || "";
  //           }
  //         });
  //         return newTerms;
  //       });

  //       // Update ownerRefs
  //       ownerRefs.current = ownerRefs.current.filter(
  //         (_, idx) => idx !== indexToDelete
  //       );

  //       await dispatch(fetchOwnerList(unitId)); // Refresh list
  //     } catch (error) {
  //       console.error("🚨 Failed to delete owner:", error);
  //       setLocalError("Failed to delete owner. Please try again.");
  //     }
  //   }
  //   setShowConfirmation(false);
  //   setIndexToDelete(null);
  // };

  const handleCancelDelete = () => {
    setShowConfirmation(false);
    setIndexToDelete(null);
  };

  // Clear messages when component mounts and unmounts
  useEffect(() => {
    dispatch(clearMessage());
    return () => {
      dispatch(clearMessage());
    };
  }, []);

  // Fetch owners for the given unit when component mounts
  useEffect(() => {
    dispatch(fetchOwnerList(unitId));
  }, [dispatch, unitId]);

  // Add another effect to clear messages when navigating
  useEffect(() => {
    const handleBeforeUnload = () => {
      dispatch(clearMessage());
    };

    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [dispatch]);

  // Updates form fields when a new member is added
  useEffect(() => {
    if (ownerState.createdMember) {
      // Check if member already exists in any ownership block
      const isDuplicate = fields.some(
        (_, index) =>
          watch(`owners.${index}.memberId`) === ownerState.createdMember.id
      );

      // if (isDuplicate) {
      //   setLocalError("This member has already been added as an owner.");
      //   dispatch(clearCreatedMember());
      //   dispatch(clearMessage());
      //   setIsMemberModalOpen(false);
      //   return;
      // }

      const emptyIndex = fields.findIndex(
        (_, index) => !watch(`owners.${index}.memberId`)
      );
      const indexToUse = emptyIndex !== -1 ? emptyIndex : fields.length;

      if (emptyIndex === -1) {
        append({
          memberId: "",
          ownershipPercentage: "",
          dateofOwnership: "",
          document: [],
          docLinks: [],
          ownershipTransferFromId: "",
          isExisting: false
        });
      }

      setTimeout(() => {
        setValue(`owners.${indexToUse}.memberId`, ownerState.createdMember.id, {
          shouldValidate: true
        });
        setValue(`owners.${indexToUse}.isExisting`, false);
        setSearchTerms((prev) => ({
          ...prev,
          [indexToUse]: ownerState.createdMember.full_name
        }));

        ownerRefs.current[indexToUse]?.current?.scrollIntoView({
          behavior: "smooth"
        });
        ownerRefs.current[indexToUse]?.current?.querySelector("input")?.focus();

        dispatch(clearCreatedMember());
        dispatch(clearMessage());
        setIsMemberModalOpen(false);
      }, 100);
    }
  }, [ownerState.createdMember, fields, watch, setValue, append, dispatch]);

  // Add this effect to set initial values when owners are loaded
  useEffect(() => {
    if (ownerState.ownerList?.owners?.length > 0 && !initialValues) {
      const owners = ownerState.ownerList.owners.map((owner) => ({
        id: owner.id,
        memberId: owner.member.id,
        ownershipPercentage: owner.ownership_percentage,
        dateofOwnership: owner.date_of_ownership
          ? new Date(owner.date_of_ownership).toISOString().split("T")[0]
          : "",
        document: [],
        docLinks:
          owner.docs?.map((doc) => ({ id: doc.id, url: doc.url })) || [],
        ownershipTransferFromId: owner.ownership_transfer_from?.id || "",
        isExisting: true
      }));
      setInitialValues({ owners });
    }
  }, [ownerState.ownerList, initialValues]);

  // Function to check if form has actual changes
  const hasActualChanges = () => {
    if (!initialValues) return false;

    const currentValues = watch();
    const currentOwners = currentValues.owners || [];
    const initialOwners = initialValues.owners || [];

    // Check if number of owners changed
    if (currentOwners.length !== initialOwners.length) return true;

    // Check each owner's fields
    return currentOwners.some((owner, index) => {
      const initialOwner = initialOwners[index];
      if (!initialOwner) return true;

      return (
        owner.memberId !== initialOwner.memberId ||
        owner.ownershipPercentage !== initialOwner.ownershipPercentage ||
        owner.dateofOwnership !== initialOwner.dateofOwnership ||
        owner.ownershipTransferFromId !==
        initialOwner.ownershipTransferFromId ||
        JSON.stringify(owner.docLinks) !== JSON.stringify(initialOwner.docLinks)
      );
    });
  };

  // Add this function to validate ownership transfer
  const validateOwnershipTransfer = (data) => {
    const owners = data.owners || [];
    const transferMap = new Map(); // Map to track transfers from each owner

    // First pass: collect all transfers
    owners.forEach((owner) => {
      if (owner.ownershipTransferFromId) {
        const fromId = owner.ownershipTransferFromId;
        const percentage = parseFloat(owner.ownershipPercentage) || 0;

        if (!transferMap.has(fromId)) {
          transferMap.set(fromId, 0);
        }
        transferMap.set(fromId, transferMap.get(fromId) + percentage);
      }
    });

    // Second pass: validate transfers
    for (const [fromId, totalTransferred] of transferMap) {
      const originalOwner = initialValues?.owners?.find(
        (o) => o.memberId === fromId
      );
      if (originalOwner) {
        const originalPercentage = parseFloat(originalOwner.ownershipPercentage) || 0;
        if (totalTransferred > originalPercentage) {
          return `Total transfer amount (${totalTransferred}%) exceeds original ownership (${originalPercentage}%)`;
        }
      }
    }

    // Validate that no owner is transferring to themselves
    const selfTransfer = owners.some(owner =>
      owner.ownershipTransferFromId === owner.memberId
    );
    if (selfTransfer) {
      return "An owner cannot transfer ownership to themselves";
    }

    return null;
  };

  // Add this function to calculate transferred percentages
  const calculateTransferredPercentages = () => {
    const newTransferredPercentages = {};

    // Get all owners with transfers
    const ownersWithTransfers = fields.filter((_, index) =>
      watch(`owners.${index}.ownershipTransferFromId`)
    );

    // Calculate total transferred percentage for each owner
    ownersWithTransfers.forEach((_, index) => {
      const transferFromId = watch(`owners.${index}.ownershipTransferFromId`);
      const transferPercentage = parseFloat(watch(`owners.${index}.ownershipPercentage`) || 0);

      if (transferFromId) {
        newTransferredPercentages[transferFromId] = (newTransferredPercentages[transferFromId] || 0) + transferPercentage;
      }
    });

    setTransferredPercentages(newTransferredPercentages);
  };

  // Add effect to recalculate transferred percentages when ownership changes
  useEffect(() => {
    calculateTransferredPercentages();
  }, [watch('owners')]);

  // Add this function to update transferred percentages when new owner percentage changes
  const updateTransferredPercentage = (newOwnerIndex, newPercentage) => {
    const transferFromId = watch(`owners.${newOwnerIndex}.ownershipTransferFromId`);
    if (!transferFromId) return;

    const newTransferredPercentages = { ...transferredPercentages };

    // Reset all transfers from this old owner
    Object.keys(newTransferredPercentages).forEach(key => {
      if (key === transferFromId) {
        newTransferredPercentages[key] = 0;
      }
    });

    // Add up all transfers from this old owner
    fields.forEach((_, index) => {
      if (!watch(`owners.${index}.isExisting`)) {
        const currentTransferFromId = watch(`owners.${index}.ownershipTransferFromId`);
        if (currentTransferFromId === transferFromId) {
          const currentPercentage = parseFloat(watch(`owners.${index}.ownershipPercentage`) || 0);
          newTransferredPercentages[transferFromId] = (newTransferredPercentages[transferFromId] || 0) + currentPercentage;
        }
      }
    });

    setTransferredPercentages(newTransferredPercentages);
  };

  // Add effect to watch all ownership changes
  useEffect(() => {
    const subscription = watch((value, { name, type }) => {
      if (name?.includes('ownershipPercentage') || name?.includes('ownershipTransferFromId')) {
        // Get all new owners
        const newOwners = fields.filter((_, index) => !watch(`owners.${index}.isExisting`));

        // Reset all transferred percentages
        const newTransferredPercentages = {};

        // Calculate new percentages for each old owner
        newOwners.forEach((_, index) => {
          const transferFromId = watch(`owners.${index}.ownershipTransferFromId`);
          const transferPercentage = parseFloat(watch(`owners.${index}.ownershipPercentage`) || 0);

          if (transferFromId) {
            // Update transferred percentage
            newTransferredPercentages[transferFromId] = (newTransferredPercentages[transferFromId] || 0) + transferPercentage;
          }
        });

        // Update all old owners' displayed percentages
        fields.forEach((_, index) => {
          if (watch(`owners.${index}.isExisting`)) {
            const memberId = watch(`owners.${index}.memberId`);
            const originalPercentage = parseFloat(originalPercentages[memberId] || 0);
            const totalTransferred = newTransferredPercentages[memberId] || 0;
            const adjustedPercentage = originalPercentage - totalTransferred;

            // Only update if the value is different to prevent infinite loop
            const currentValue = watch(`owners.${index}.ownershipPercentage`);
            if (currentValue !== adjustedPercentage.toFixed(2)) {
              setValue(`owners.${index}.ownershipPercentage`, adjustedPercentage.toFixed(2), { shouldValidate: false });
            }
          }
        });

        setTransferredPercentages(newTransferredPercentages);
      }
    });

    return () => subscription.unsubscribe();
  }, [watch, fields, setValue, originalPercentages]);

  // Add this function to detect owners that should be removed
  const shouldRemoveOwner = (memberId) => {
    const originalPercentage = parseFloat(originalPercentages[memberId] || 0);
    const transferredPercentage = transferredPercentages[memberId] || 0;
    // Use a small epsilon to handle floating point comparison
    return Math.abs(originalPercentage - transferredPercentage) < 0.01;
  };

  // Add this function to handle owner removal
  // const handleOwnerRemoval = async (memberId) => {
  //   const ownerToRemove = ownerState.ownerList?.owners?.find(
  //     (o) => o.member.id === memberId
  //   );
  //   if (ownerToRemove) {
  //     try {
  //       await dispatch(deleteOwner(ownerToRemove.id)).unwrap();
  //       // Remove from the form array
  //       const indexToRemove = fields.findIndex(
  //         (_, idx) => watch(`owners.${idx}.memberId`) === memberId
  //       );
  //       if (indexToRemove !== -1) {
  //         remove(indexToRemove);
  //       }
  //     } catch (error) {
  //       console.error("Failed to remove owner:", error);
  //       setLocalError("Failed to remove owner. Please try again.");
  //     }
  //   }
  // };

  // Modify the handleOwnershipPercentageChange function
  const handleOwnershipPercentageChange = (e, field, index) => {
    const value = e.target.value;
    field.onChange(value);

    // Get the old owner's ID from transfer from
    const transferFromId = watch(`owners.${index}.ownershipTransferFromId`);
    if (transferFromId) {
      // Find the old owner in the existing owners
      const oldOwnerIndex = fields.findIndex(
        (_, idx) => watch(`owners.${idx}.memberId`) === transferFromId && watch(`owners.${idx}.isExisting`)
      );

      if (oldOwnerIndex !== -1) {
        // Get the original percentage of the old owner
        const originalPercentage = parseFloat(originalPercentages[transferFromId] || 0);

        // Calculate total transferred percentage for this old owner
        const totalTransferred = fields.reduce((sum, _, idx) => {
          if (!watch(`owners.${idx}.isExisting`) &&
            watch(`owners.${idx}.ownershipTransferFromId`) === transferFromId) {
            return sum + parseFloat(watch(`owners.${idx}.ownershipPercentage`) || 0);
          }
          return sum;
        }, 0);

        // Calculate the remaining percentage for the old owner
        const remainingPercentage = originalPercentage - totalTransferred;

        // Check if owner should be removed
        if (shouldRemoveOwner(transferFromId)) {
          handleOwnerRemoval(transferFromId);
        } else {
          // Only update if the value is different to prevent infinite loop
          const currentValue = watch(`owners.${oldOwnerIndex}.ownershipPercentage`);
          if (currentValue !== remainingPercentage.toFixed(2)) {
            setValue(`owners.${oldOwnerIndex}.ownershipPercentage`, remainingPercentage.toFixed(2), { shouldValidate: false });
          }
        }

        // Update transferred percentages
        const newTransferredPercentages = { ...transferredPercentages };
        newTransferredPercentages[transferFromId] = totalTransferred;
        setTransferredPercentages(newTransferredPercentages);
      }
    }
  };

  // Modify the onSubmit function to handle removals
  const onSubmit = async (data) => {
    console.log("➡️ Submitting form data:", data);
    setHasFileChanges(false);

    // Validate ownership transfer
    const transferError = validateOwnershipTransfer(data);
    if (transferError) {
      setTransferValidationError(transferError);
      return;
    }
    setTransferValidationError(null);

    // Calculate total ownership with precise decimal handling
    const totalOwnership = data.owners.reduce((sum, owner) => {
      // For existing owners, use their adjusted percentage (original - transferred)
      if (owner.isExisting) {
        const memberId = owner.memberId;
        const originalPercentage = parseFloat(originalPercentages[memberId] || 0);
        const transferredPercentage = transferredPercentages[memberId] || 0;
        return sum + (originalPercentage - transferredPercentage);
      }
      // For new owners, add their percentage directly
      return sum + parseFloat(owner.ownershipPercentage || 0);
    }, 0);

    // Round to 2 decimal places for comparison
    const roundedTotal = Math.round(totalOwnership * 100) / 100;

    console.log('📊 Ownership Total:', {
      total: roundedTotal,
      owners: data.owners.map(owner => ({
        name: searchTerms[data.owners.indexOf(owner)],
        percentage: owner.isExisting
          ? (parseFloat(originalPercentages[owner.memberId] || 0) - (transferredPercentages[owner.memberId] || 0)).toFixed(2)
          : parseFloat(owner.ownershipPercentage || 0).toFixed(2)
      }))
    });

    // Check if total ownership is exactly 100%
    if (roundedTotal !== 100) {
      setLocalError(
        `Total ownership percentage must be exactly 100%. Current total: ${roundedTotal}%`
      );
      return;
    }

    // Validate individual ownership percentages
    const invalidOwnership = data.owners.some((owner) => {
      const percentage = parseFloat(owner.ownershipPercentage || 0);
      return percentage <= 0 || percentage > 100;
    });

    if (invalidOwnership) {
      setLocalError(
        "Each ownership percentage must be greater than 0 and less than or equal to 100%"
      );
      return;
    }

    const memberIds = data.owners.map((owner) => owner.memberId);

    try {
      // First, identify owners to be removed (those with 100% transfer)
      const ownersToRemove = new Set();
      data.owners.forEach((owner, index) => {
        if (owner.isExisting) {
          const memberId = owner.memberId;
          if (shouldRemoveOwner(memberId)) {
            ownersToRemove.add(memberId);
          }
        }
      });

      const ownerPromises = data.owners.map(async (owner, index) => {
        // Skip if this owner is marked for removal
        if (ownersToRemove.has(owner.memberId)) {
          return Promise.resolve();
        }

        if (
          !owner.memberId ||
          !owner.ownershipPercentage ||
          !owner.dateofOwnership
        ) {
          console.error(
            `❗ Owner data incomplete at index ${index}. Skipping.`
          );
          return Promise.resolve();
        }

        // Check if this member is already an owner
        const existingOwner = ownerState.ownerList?.owners?.find(
          (o) => o.member.id === owner.memberId
        );

        const formData = new FormData();
        formData.append("member", owner.memberId);
        formData.append("unit", parseInt(unitId, 10));

        // Calculate the final ownership percentage
        let finalPercentage;
        if (owner.isExisting) {
          // For existing owners, subtract transferred amount
          const originalPercentage = parseFloat(originalPercentages[owner.memberId] || 0);
          const transferredPercentage = transferredPercentages[owner.memberId] || 0;
          finalPercentage = originalPercentage - transferredPercentage;
        } else {
          // For new owners, if they are receiving a transfer, add it to their existing ownership
          const existingOwnership = parseFloat(originalPercentages[owner.memberId] || 0);
          const transferAmount = parseFloat(owner.ownershipPercentage || 0);
          finalPercentage = existingOwnership + transferAmount;
        }

        formData.append("ownership_percentage", finalPercentage);
        formData.append("date_of_ownership", formatDate(owner.dateofOwnership));
        formData.append(
          "ownership_transfer_from", ""
        );

        // Handle file uploads
        if (owner.document && owner.document.length > 0) {
          owner.document.forEach((file) => {
            if (file instanceof File) {
              formData.append("owner_docs_upload", file);
            }
          });
        }

        // Handle docs to delete
        const dbId = owner.id;
        const removedDocIds = dbId ? docsToDelete[dbId] || [] : [];
        removedDocIds.forEach((docId) => {
          formData.append("docs_to_delete[]", docId);
        });

        console.log(
          `📦 Payload for ${dbId ? "Update" : "Create"
          } Owner (index ${index}):`,
          {
            member: owner.memberId,
            unit: unitId,
            ownershipPercentage: finalPercentage.toFixed(2),
            date_of_ownership: formatDate(owner.dateofOwnership),
            documents: owner.document?.length || 0,
            ownership_transfer_from: owner.ownershipTransferFromId,
            docs_to_delete: removedDocIds
          }
        );

        try {
          // If we have an existing owner record, update it instead of creating a new one
          if (existingOwner) {
            return await dispatch(
              updateOwner({ ownerId: existingOwner.id, formData })
            ).unwrap();
          } else if (dbId) {
            return await dispatch(
              updateOwner({ ownerId: dbId, formData })
            ).unwrap();
          } else {
            return await dispatch(createOwner(formData)).unwrap();
          }
        } catch (error) {
          console.error(`Error processing owner at index ${index}:`, error);
          throw new Error(error.message || "Failed to process owner");
        }
      });

      // Add promises to delete owners with 100% transfer
      const deletePromises = Array.from(ownersToRemove).map(async (memberId) => {
        const ownerToDelete = ownerState.ownerList?.owners?.find(
          (o) => o.member.id === memberId
        );
        if (ownerToDelete) {
          return await dispatch(deleteOwner(ownerToDelete.id)).unwrap();
        }
      });

      // Wait for all operations to complete
      await Promise.all([...ownerPromises, ...deletePromises]);
      console.log("✅ All owners submitted successfully.");

      await dispatch(fetchOwnerList(unitId));
      setDocsToDelete({}); // Clear after submission

      if (memberIds) {
        dispatch(fetchMemberById(memberIds));
      }

      // Clear all states after successful submission
      setSearchTerms({});
      setLocalError(null);
      setTransferValidationError(null);
      setHasFileChanges(false);
      setInitialValues(null);
      setTransferredPercentages({});
      setOriginalPercentages({});
      setHasTransfers(false);
      setShowConfirmation(false);
      setIndexToDelete(null);
      setIsMemberModalOpen(false);
      setShowCompanyModal(false);

      // Clear form fields array
      replace([]);
      ownerRefs.current = [];

      // Show success message and redirect
      dispatch({
        type: "owner/setSuccessMessage",
        payload: "Ownership details updated successfully"
      });
    } catch (error) {
      console.error("🚨 Error submitting form:", error);
      setLocalError(
        error?.message ||
        "Failed to submit owner details. Please check all required fields and try again."
      );
    }
  };

  return (
    <>
      <AddCompany
        isOpen={showCompanyModal}
        onClose={() => setShowCompanyModal(false)}
        fields={fields}
      />

      {(localError ||
        transferValidationError ||
        ownerState.successMessage ||
        ownerState.error) && (
          <MessageBox
            message={
              localError ||
              transferValidationError ||
              ownerState.successMessage ||
              ownerState.error
            }
            error={!!(localError || transferValidationError || ownerState.error)}
            clearMessage={() => {
              if (localError) {
                setLocalError(null);
              } else if (transferValidationError) {
                setTransferValidationError(null);
              } else {
                dispatch(clearMessage());
              }
            }}
            onOk={() => {
              if (localError) {
                setLocalError(null);
              } else if (transferValidationError) {
                setTransferValidationError(null);
              } else {
                dispatch(clearMessage());
                if (
                  ownerState.successMessage &&
                  !ownerState.successMessage.toLowerCase().includes("delete")
                ) {
                  //navigate(`/unit-details/${unitId}?tab=2`);
                  dispatch(setActiveTabs(3));
                  navigate(-1);
                }
              }
            }}
          />
        )}

      {/* {showConfirmation && (
        <ConfirmationMessageBox
          message="Are you sure you want to delete this Owner?"
          onConfirm={handleConfirmDelete}
          onCancel={handleCancelDelete}
        />
      )} */}

      <form onSubmit={handleSubmit(onSubmit)} className="p-5">
        {/* Current Ownership Details Section */}
        {fields.some((field) =>
          watch(`owners.${fields.indexOf(field)}.isExisting`)
        ) && (
            <div className="mb-8">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-base font-semibold mb-4 text-teal-600">
                  Current Ownership Details
                </h3>
                <button
                  type="button"
                  onClick={() =>
                    append({
                      memberId: "",
                      ownershipPercentage: "",
                      dateofOwnership: "",
                      document: [],
                      docLinks: [],
                      ownershipTransferFromId: "",
                      isExisting: false
                    })
                  }
                  className="w-10 h-10 text-xl text-teal-600 font-bold rounded-full border border-teal-600 hover:bg-teal-50 flex items-center justify-center transition"
                >
                  <FaPlus size={12} />
                </button>
              </div>
              {fields.map((field, index) => {
                if (!watch(`owners.${index}.isExisting`)) return null;

                const memberId = watch(`owners.${index}.memberId`);
                const originalPercentage = parseFloat(originalPercentages[memberId] || 0);
                const transferredPercentage = transferredPercentages[memberId] || 0;
                const adjustedPercentage = originalPercentage - transferredPercentage;

                console.log('📊 Old Owner Details:', {
                  ownerId: memberId,
                  originalPercentage: originalPercentage.toFixed(2) + '%',
                  transferredPercentage: transferredPercentage.toFixed(2) + '%',
                  adjustedPercentage: adjustedPercentage.toFixed(2) + '%'
                });

                return (
                  <div
                    key={field.id}
                    className="relative bg-gray-50 border rounded-2xl p-4 shadow-sm mb-4"
                  >
                    <div className="flex items-start justify-between">
                      <h4 className="text-sm font-medium mb-3 text-gray-600">
                        {getOrdinal(index + 1)} Ownership Details
                      </h4>
                    </div>

                    <div className="grid grid-cols-12 gap-2 lg:w-[687px]">
                      <div className="login-field col-span-5">
                        <Paragraph className="my-2 text-xs font-medium text-gray-600">
                          Unit Owner Name
                        </Paragraph>
                        <input
                          type="text"
                          value={searchTerms[index] || ""}
                          onChange={(e) => {
                            setSearchTerms((prev) => ({
                              ...prev,
                              [index]: e.target.value
                            }));
                          }}
                          className="w-full border px-2 py-1.5 rounded bg-gray-100"
                          disabled={true}
                          readOnly={true}
                        />
                      </div>

                      <div className="login-field col-span-3">
                        <Paragraph className="my-2 text-xs font-medium text-gray-600">
                          Ownership Percentage
                        </Paragraph>
                        <input
                          type="text"
                          value={hasTransfers ? adjustedPercentage.toFixed(2) + '%' : originalPercentage.toFixed(2) + '%'}
                          className="w-full border px-2 py-1.5 rounded bg-gray-100"
                          disabled={true}
                          readOnly={true}
                        />
                      </div>

                      <div className="login-field col-span-4">
                        <Paragraph className="my-2 text-xs font-medium text-gray-600">
                          Date of Ownership
                        </Paragraph>
                        <input
                          type="date"
                          {...register(`owners.${index}.dateofOwnership`)}
                          className="w-full border px-2 py-1.5 rounded bg-gray-100"
                          disabled={true}
                          readOnly={true}
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 gap-2 lg:w-[687px] mt-3">
                      <div className="login-field">
                        {watch(`owners.${index}.docLinks`)?.length > 0 && (
                          <>
                            <Paragraph className="my-2 text-xs font-medium text-gray-600">
                              Ownership Documents
                            </Paragraph>

                            <FileDropzone
                              files={[]}
                              docLinks={watch(`owners.${index}.docLinks`) || []}
                              onDrop={() => { }}
                              onRemove={() => { }}
                              disabled={true}
                              readOnly={true}
                              showRemoveButton={false}
                              showUploadButton={false}
                              showDropzone={false}
                              hideDropzoneMessage={true}
                            />
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}

        {/* New Ownership Details Section */}
        {fields.some(
          (field) => !watch(`owners.${fields.indexOf(field)}.isExisting`)
        ) && (
            <div>
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-base font-semibold text-teal-600">
                  New Ownership Details
                </h3>
                <div className="flex flex-wrap gap-4">
                  <button
                    type="button"
                    className={`font-medium px-4 py-2 rounded-lg shadow text-sm ${fields.some(
                      (_, index) =>
                        !watch(`owners.${index}.isExisting`) &&
                        !watch(`owners.${index}.memberId`)
                    )
                      ? "bg-teal-600 hover:bg-teal-700 text-white"
                      : "bg-gray-300 text-gray-500 cursor-not-allowed"
                      }`}
                    onClick={() => setIsMemberModalOpen(true)}
                    disabled={
                      !fields.some(
                        (_, index) =>
                          !watch(`owners.${index}.isExisting`) &&
                          !watch(`owners.${index}.memberId`)
                      )
                    }
                  >
                    + Add New Comm Member
                  </button>
                  <button
                    type="button"
                    className={`font-medium px-4 py-2 rounded-lg shadow text-sm ${fields.some(
                      (_, index) =>
                        !watch(`owners.${index}.isExisting`) &&
                        !watch(`owners.${index}.memberId`)
                    )
                      ? "bg-teal-600 hover:bg-teal-700 text-white"
                      : "bg-gray-300 text-gray-500 cursor-not-allowed"
                      }`}
                    onClick={() => setShowCompanyModal(true)}
                    disabled={
                      !fields.some(
                        (_, index) =>
                          !watch(`owners.${index}.isExisting`) &&
                          !watch(`owners.${index}.memberId`)
                      )
                    }
                  >
                    + Add New Company
                  </button>
                </div>
              </div>
              {fields.map((field, index) => {
                if (watch(`owners.${index}.isExisting`)) return null;

                const filePath = `owners.${index}.document`;
                const files = watch(filePath) || [];
                const docLinks = watch(`owners.${index}.docLinks`) || [];
                ownerRefs.current[index] =
                  ownerRefs.current[index] || React.createRef();

                return (
                  <div
                    ref={ownerRefs.current[index]}
                    key={field.id}
                    className="relative bg-white border rounded-2xl p-4 shadow-sm mb-4"
                  >
                    <div className="flex items-start justify-between">
                      <h4 className="text-sm font-medium mb-3 text-gray-600">
                        {getOrdinal(index + 1)} Ownership Details
                      </h4>
                      <div className="flex gap-2">
                        {fields.length > 1 && (
                          <button
                            type="button"
                            onClick={() => handleRemoveOwner(index)}
                            className="w-10 h-10 text-xl text-teal-600 font-bold rounded-full border border-teal-600 hover:bg-teal-50 flex items-center justify-center transition"
                          >
                            <FaMinus size={12} />
                          </button>
                        )}
                        <button
                          type="button"
                          onClick={() =>
                            append({
                              memberId: "",
                              ownershipPercentage: "",
                              dateofOwnership: "",
                              document: [],
                              docLinks: [],
                              ownershipTransferFromId: "",
                              isExisting: false
                            })
                          }
                          className="w-10 h-10 text-xl text-teal-600 font-bold rounded-full border border-teal-600 hover:bg-teal-50 flex items-center justify-center transition"
                        >
                          <FaPlus size={12} />
                        </button>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 gap-2 lg:w-[687px]">
                      <div className="login-field">
                        <Paragraph className="my-2 text-xs font-medium text-gray-600">
                          Transfer Ownership From*
                        </Paragraph>
                        <MemberSearchAutocomplete
                          value={searchTerms[`from_${index}`] || ""}
                          memberId={watch(`owners.${index}.ownershipTransferFromId`) || ""}
                          onSelect={(member) => {
                            // Update the search terms
                            setSearchTerms((prev) => ({
                              ...prev,
                              [`from_${index}`]: member.full_name
                            }));
                            
                            // Update the form field
                            setValue(`owners.${index}.ownershipTransferFromId`, member.id, {
                              shouldValidate: true
                            });
                          }}
                          unitId={unitId}
                          isOwnerSearch={true}
                          disabled={false}
                          readOnly={false}
                          isDisabled={false}
                          hideClearButton={false}
                        />

                        {errors.owners?.[index]?.ownershipTransferFromId && (
                          <p className="text-red-500 text-xs mt-1">
                            {errors.owners[index].ownershipTransferFromId.message}
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="grid grid-cols-1 gap-2 lg:w-[687px]">
                      <div className="login-field">
                        <Paragraph className="my-2 text-xs font-medium">
                          Transfer Ownership To*
                        </Paragraph>
                        <MemberSearchAutocomplete
                          value={searchTerms[index] || ""}
                          memberId={watch(`owners.${index}.memberId`) || ""}
                          onSelect={(member) => {
                            // Update the search terms
                            setSearchTerms((prev) => ({
                              ...prev,
                              [index]: member.full_name
                            }));
                            
                            // Update the form field
                            setValue(`owners.${index}.memberId`, member.id, {
                              shouldValidate: true
                            });
                          }}
                          unitId={unitId}
                          isOwnerSearch={false}
                          disabled={false}
                          readOnly={false}
                          isDisabled={false}
                          hideClearButton={false}
                        />

                        {errors.owners?.[index]?.memberId && (
                          <p className="text-red-500 text-xs mt-1">
                            {errors.owners[index].memberId.message}
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-2 lg:w-[687px]">
                      <div className="login-field">
                        <Paragraph className="my-2 text-xs font-medium">
                          Ownership Percentage*
                        </Paragraph>
                        <Controller
                          name={`owners.${index}.ownershipPercentage`}
                          control={control}
                          render={({ field }) => (
                            <NumberInputComponent
                              {...field}
                              placeholder="%"
                              step="0.01"
                              onChange={(e) => handleOwnershipPercentageChange(e, field, index)}
                            />
                          )}
                        />
                        {errors.owners?.[index]?.ownershipPercentage && (
                          <p className="text-red-500 text-xs mt-1">
                            {errors.owners[index].ownershipPercentage.message}
                          </p>
                        )}
                      </div>

                      <div className="login-field">
                        <Paragraph className="my-2 text-xs font-medium">
                          Date of Ownership*
                        </Paragraph>
                        <input
                          type="date"
                          {...register(`owners.${index}.dateofOwnership`)}
                          className="w-full border px-2 py-1.5 rounded"
                        />
                        {errors.owners?.[index]?.dateofOwnership && (
                          <p className="text-red-500 text-xs mt-1">
                            {errors.owners[index].dateofOwnership.message}
                          </p>
                        )}
                      </div>

                      <div className="login-field col-span-2">
                        <Paragraph className="my-2 text-xs font-medium">
                          Upload Ownership Documents
                        </Paragraph>

                        <FileDropzone
                          files={files}
                          docLinks={docLinks}
                          onDrop={(acceptedFiles) => {
                            const newFiles = [...files, ...acceptedFiles];
                            if (newFiles.length > 5) {
                              setError(`owners.${index}.document`, {
                                type: "manual",
                                message: "You can upload a maximum of 5 documents."
                              });
                              return;
                            }
                            setValue(filePath, newFiles, {
                              shouldDirty: true,
                              shouldValidate: true
                            });
                            clearErrors(`owners.${index}.document`);
                          }}
                          onRemove={(idx, type) => {
                            if (type === "file") {
                              const updatedFiles = files.filter((_, i) => i !== idx);
                              setValue(filePath, updatedFiles, {
                                shouldDirty: true,
                                shouldValidate: true
                              });
                              if (updatedFiles.length <= 5) {
                                clearErrors(`owners.${index}.document`);
                              }
                            } else if (type === "docLink") {
                              const removedDoc = docLinks[idx];
                              if (removedDoc && removedDoc.id) {
                                const ownerDbId = watch(`owners.${index}.id`);
                                if (ownerDbId) {
                                  setDocsToDelete((prev) => ({
                                    ...prev,
                                    [ownerDbId]: [...(prev[ownerDbId] || []), removedDoc.id]
                                  }));
                                }
                              }
                              const updatedDocLinks = docLinks.filter((_, i) => i !== idx);
                              setValue(`owners.${index}.docLinks`, updatedDocLinks, {
                                shouldDirty: true,
                                shouldValidate: true
                              });
                            }
                          }}
                          showRemoveButton={true}
                          showUploadButton={true}
                          showDropzone={true}
                        />

                        {errors.owners?.[index]?.document && (
                          <div className="text-red-500 text-xs mt-2">
                            {errors.owners[index].document.message}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}

        <div className="mt-6">
          <button
            type="submit"
            className={`w-full py-2 px-4 rounded ${hasActualChanges() || hasFileChanges
              ? "bg-teal-600 text-white"
              : "bg-gray-300 text-gray-500 cursor-not-allowed"
              }`}
            disabled={ownerState.loading || (!hasActualChanges() && !hasFileChanges)}
          >
            {ownerState.loading ? "Saving..." : "Save"}
          </button>
        </div>
      </form>

      <AddMemberForm
        isOpen={isMemberModalOpen}
        onClose={() => setIsMemberModalOpen(false)}
        unitId={unitId}
      />
    </>
  );
};

export default ChangeOwnerForm;