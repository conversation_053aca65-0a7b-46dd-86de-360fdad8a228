import React, { useEffect } from "react";
import PropTypes from "prop-types";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import RadioComponent from "Components/FormComponent/RadioComponent";
import SelectComponent from "Components/FormComponent/SelectComponent";
import TextInputComponent from "Components/FormComponent/TextInputComponent";
import ErrorMessage from "Components/MessageBox/ErrorMessage";
import Heading from "Components/HeadingComponent/Heading";
import { Div } from "Components/Ui/Div";
import { Span } from "Components/Ui/Span";
import FileComponents from "../../../Components/UtilsComponents/FileComponents";
import { updateChangedFields } from "../../../utils/updateFileChange";

const MemberMainForm = ({
  formData,
  setFormData,
  memberFields,
  errors,
  handleChange,
  onFileChange,
  savedFront,
  savedBack,
  disabled,setIsFormChangedFirstTab
}) => {
  const handleDateChange = (date, e) => {
    setFormData((prevData) => ({
      ...prevData,
      [e.target.name]: date ? formatDate(date) : ""
    }));

    updateChangedFields(setIsFormChangedFirstTab ,[e.target.name], date ? formatDate(date) : "")
  };

  const formatDate = (date) => {
    const formattedDate = date
      .toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "short",
        year: "numeric"
      })
      .replace(/ /g, "-");
    return formattedDate.replace("Sept", "Sep");
  };

  const currentYear = new Date().getFullYear();

  return (
    <Div>

      <Heading title="General Information" size="lg" color="text-primary" />

      {/* Full Name */}
      <TextInputComponent
        value={formData.full_name || ""}
        onChange={handleChange}
        name={memberFields.full_name.name}
        label={
          <Span>
            {memberFields.full_name.label}{" "}
            <Span className="text-red-500">*</Span>
          </Span>
        }
        placeholder={memberFields.full_name.label}
        field={memberFields.full_name}
        disabled={disabled}
      />
      <ErrorMessage message={errors.full_name} />

      {/* Email */}
      <TextInputComponent
        value={formData.general_email || ""}
        onChange={handleChange}
        name={memberFields.general_email.name}
        label={
          <Span>
            {memberFields.general_email.label}{" "}
            <Span className="text-red-500">*</Span>
          </Span>
        }
        placeholder={memberFields.general_email.label}
        field={memberFields.general_email}
        disabled={disabled}
      />
      <ErrorMessage message={errors.general_email} />

      {/* Contact Number */}
      <TextInputComponent
        value={formData.general_contact || ""}
        onChange={handleChange}
        name={memberFields.general_contact.name}
        label={
          <Span>
            {memberFields.general_contact.label}{" "}
            <Span className="text-red-500">*</Span>
          </Span>
        }
        placeholder={memberFields.general_contact.label}
        field={memberFields.general_contact}
        disabled={disabled}
      />
      <ErrorMessage message={errors.general_contact} />

      {/* NID Number */}
      <TextInputComponent
        value={formData.nid_number || ""}
        onChange={handleChange}
        name={memberFields.nid_number.name}
        label={memberFields.nid_number.label}
        placeholder={memberFields.nid_number.label}
        field={memberFields.nid_number}
        disabled={disabled}
      />
      <ErrorMessage message={errors.nid_number} />

      {/* Addresses */}
      <TextInputComponent
        value={formData.permanent_address || ""}
        onChange={handleChange}
        name={memberFields.permanent_address.name}
        label={memberFields.permanent_address.label}
        placeholder={memberFields.permanent_address.label}
        field={memberFields.permanent_address}
        disabled={disabled}
      />
      <TextInputComponent
        value={formData.present_address || ""}
        onChange={handleChange}
        name={memberFields.present_address.name}
        label={memberFields.present_address.label}
        placeholder={memberFields.present_address.label}
        field={memberFields.present_address}
        disabled={disabled}
      />

      {/* Date of Birth */}
      {/* <Div className="login-field">
        <DatePicker
          className={`login-field-input ${
            disabled ? "bg-disabledInput cursor-not-allowed text-grey100" : ""
          }`}
          name="date_of_birth"
          selected={
            formData.date_of_birth ? new Date(formData.date_of_birth) : null
          }
          onChange={(date) =>
            handleDateChange(date, { target: { name: "date_of_birth" } })
          }
          dateFormat="dd-MMM-yyyy"
          placeholderText="Date Of Birth"
          showYearDropdown
          showMonthDropdown
          scrollableYearDropdown
          yearDropdownItemNumber={90}
          minDate={new Date("1950-01-01")}
          maxDate={new Date(`${currentYear}-12-31`)}
          disabled={disabled}
        
        />
      </Div> */}

      {/* Occupation & Gender */}
      <Div class=" flex justify-between item-center gap-4 ">
        <Div className="login-field">
          <div className="my-2 text-left">
            <label className="">Date Of Birth</label>
          </div>
          <DatePicker
            className={`login-field-input ${disabled ? 'bg-disabledInput cursor-not-allowed text-black100' : 'cursor-pointer'}`}
            name="date_of_birth"
            selected={
              formData.date_of_birth ? new Date(formData.date_of_birth) : null
            }
            onChange={(date) =>
              handleDateChange(date, { target: { name: "date_of_birth" } })
       
            }
            dateFormat="dd-MMM-yyyy"
            placeholderText=""
            showYearDropdown
            showMonthDropdown
            scrollableYearDropdown
            yearDropdownItemNumber={90}
            minDate={new Date("1950-01-01")}
            maxDate={new Date(`${currentYear}-12-31`)}
            disabled={disabled}
            
          />
        </Div>
        <TextInputComponent
          value={formData.occupation || ""}
          onChange={handleChange}
          name={memberFields.occupation.name}
          label={memberFields.occupation.label}
          placeholder={memberFields.occupation.label}
          field={memberFields.occupation}
          disabled={disabled}
        />
        <RadioComponent
          options={memberFields.gender.options}
          selectedValue={formData.gender}
          onChange={handleChange}
          name={memberFields.gender.name}
          label={memberFields.gender.label}
          disabled={disabled}
        />
      </Div>

      {/* Marital Status & Religion */}
      <Div className="flex justify-between gap-3">
        <SelectComponent
          options={memberFields.marital_status.options}
          value={formData.marital_status}
          name={memberFields.marital_status.name}
          label={memberFields.marital_status.label}
          onChange={handleChange}
          field={memberFields.marital_status}
          disabled={disabled}
        />
        <SelectComponent
          options={memberFields.religion.options}
          value={formData.religion}
          name={memberFields.religion.name}
          label={memberFields.religion.label}
          onChange={handleChange}
          field={memberFields.religion}
          disabled={disabled}
        />
      </Div>

      {/* File Upload */}
      <FileComponents
        onFileChange={onFileChange}
        savedFront={savedFront}
        savedBack={savedBack}
        allowedTypes={["image/jpeg", "image/png", "image/jpg"]}
        errorMessage="Please upload a valid image"
        disabled={disabled}
      />
    </Div>
  );
};

MemberMainForm.propTypes = {
  formData: PropTypes.object.isRequired,
  setFormData: PropTypes.func.isRequired,
  memberFields: PropTypes.object.isRequired,
  errors: PropTypes.object,
  onFileChange: PropTypes.func.isRequired,
  savedFront: PropTypes.any,
  savedBack: PropTypes.any
};

export default MemberMainForm;
