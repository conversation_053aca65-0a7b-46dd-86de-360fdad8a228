import { useState, useEffect, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { FaPlus, FaFilePdf, FaFileWord, FaImage } from "react-icons/fa";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>i<PERSON>ilter } from "react-icons/bi";
import AnnouncementHistoryModal from "../components/AnnouncementHistoryModal";
import usePinPost from "../components/PinPost";
import Calendar from "../components/Calendar";
import ConfirmationMessageBox from "../../../../Components/MessageBox/ConfirmationMessageBox";
import MessageBox from "../../../../Components/MessageBox/MessageBox";
import ImageSlider from "../../../../Components/Modal/ImageSlider";
import DocumentViewer from "../../../../Components/FileViewer/DocumentViewer";
import { useAnnouncements } from "../../../../hooks/useAnnouncements";
import AnnouncementListPreview from "./AnnouncementListPreview";
import FilterSelectModal from "../../../../Components/FilterSelect/FilterSelectModal";
import { clearUserCountCache } from "../hooks/useUserCount";

const AnnouncementList = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Redux hooks for announcements
  const {
    announcements: reduxAnnouncements,
    loading: reduxLoading,
    deleteSuccess,
    message,
    loadAnnouncements: loadAnnouncementsRedux,
    updateAllStatuses: updateAllStatusesRedux,
    removeAnnouncement: removeAnnouncementRedux,
    moveAnnouncementToExpired: moveAnnouncementToExpiredRedux,
    restoreExpiredAnnouncement: restoreExpiredAnnouncementRedux,
    loadAnnouncement: loadAnnouncementRedux,
    clearAllSuccess: clearAllSuccessRedux
  } = useAnnouncements();

  const [activeTab, setActiveTab] = useState(() => {
    return (
      parseInt(localStorage.getItem("announcementActiveTab")) ||
      location.state?.activeTab ||
      1
    );
  });
  const [myPostChecked, setMyPostChecked] = useState(false);
  const [selectedPriority, setSelectedPriority] = useState([]);
  const [selectedLabel, setSelectedLabel] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [availableLabels, setAvailableLabels] = useState([]);
  const [selectedImages, setSelectedImages] = useState([]);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isImageSliderOpen, setIsImageSliderOpen] = useState(false);
  const [openDropdownId, setOpenDropdownId] = useState(null);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [isFilterExpanded, setIsFilterExpanded] = useState(false);
  const [selectedDate, setSelectedDate] = useState("");
  const [announcementToDelete, setAnnouncementToDelete] = useState(null);
  const [showRestoreConfirmation, setShowRestoreConfirmation] = useState(false);
  const [announcementToRestore, setAnnouncementToRestore] = useState(null);
  const [showRestoreSuccess, setShowRestoreSuccess] = useState(false);
  const [restoreSuccessMessage, setRestoreSuccessMessage] = useState("");
  const [showHistoryModal, setShowHistoryModal] = useState(false);
  const [selectedAnnouncementForHistory, setSelectedAnnouncementForHistory] =
    useState(null);
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [isDocumentViewerOpen, setIsDocumentViewerOpen] = useState(false);
  const [showMoveToExpiredConfirmation, setShowMoveToExpiredConfirmation] =
    useState(false);
  const [announcementToMoveToExpired, setAnnouncementToMoveToExpired] =
    useState(null);
  const [showMoveToExpiredSuccess, setShowMoveToExpiredSuccess] =
    useState(false);
  const [moveToExpiredSuccessMessage, setMoveToExpiredSuccessMessage] =
    useState("");
  const dropdownRef = useRef(null);

  const announcements = reduxAnnouncements;
  const loading = reduxLoading;

  // Update available labels based on all announcements (not filtered by tab)
  useEffect(() => {
    if (announcements && announcements.length > 0) {
      // Extract unique labels from ALL announcements regardless of tab
      // Split comma-separated labels into individual labels
      const allLabels = announcements
        .map((ann) => ann.label)
        .filter((label) => label && label.trim() !== "")
        .flatMap((label) =>
          label
            .split(",")
            .map((l) => l.trim())
            .filter((l) => l !== "")
        );

      const uniqueLabels = [...new Set(allLabels)];
      setAvailableLabels(uniqueLabels);
    } else {
      setAvailableLabels([]);
    }
  }, [announcements]);

  const pinPost = usePinPost({
    announcements,
    setAnnouncements: () => {},
    onPinSuccess: (message) => console.log("Pin success:", message),
    onPinError: (message) => console.error("Pin error:", message),
    currentTab: activeTab,
    onMoveToExpired: (announcementId) => {
      console.log("Pin moved to expired:", announcementId);
      loadAnnouncements(); // Refresh the announcements list
      // Switch to expired tab to show the moved announcement
      setActiveTab(3);
      localStorage.setItem("announcementActiveTab", "3");
    }
  });

  useEffect(() => {
    // Clear user count cache when component mounts to ensure fresh data
    clearUserCountCache();
    loadAnnouncements();
  }, []);

  useEffect(() => {
    loadAnnouncements();
  }, [myPostChecked, selectedPriority, searchTerm]);

  useEffect(() => {
    const interval = setInterval(() => {
      updateAnnouncementStatuses();
    }, 600000);

    return () => clearInterval(interval);
  }, []);

  // Clear cache when user returns to the page/tab
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // User returned to the tab, clear cache for fresh data
        clearUserCountCache();
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);
    return () =>
      document.removeEventListener("visibilitychange", handleVisibilityChange);
  }, []);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setOpenDropdownId(null);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  useEffect(() => {
    if (location.state?.activeTab) {
      setActiveTab(location.state.activeTab);
      window.history.replaceState({}, document.title);
    }

    if (location.state?.announcementId) {
      setTimeout(() => {
        loadAnnouncements(true);
      }, 100);
      window.history.replaceState({}, document.title);
    }
  }, [location.state]);

  const loadAnnouncements = async (forceRefresh = false) => {
    try {
      const accessToken = localStorage.getItem("access_token");
      if (!accessToken) {
        console.error("No access token found. User needs to login.");
        return;
      }

      const params = {};
      if (forceRefresh) params._t = Date.now();
      if (selectedPriority.length > 0)
        params.priority = selectedPriority
          .map((p) => p.toLowerCase())
          .join(",");
      if (searchTerm) params.search = searchTerm;

      const result = await loadAnnouncementsRedux(params);
      if (result.error) {
        console.error("Error loading announcements:", result.error);
      }
    } catch (error) {
      console.error("Error loading announcements:", error);
      if (
        error.message?.includes("401") ||
        error.message?.includes("unauthorized")
      ) {
        localStorage.removeItem("access_token");
        localStorage.removeItem("refresh_token");
      }
    }
  };

  const updateAnnouncementStatuses = async () => {
    try {
      await updateAllStatusesRedux();
      loadAnnouncements();
    } catch (error) {
      console.error("Error updating announcement statuses:", error);
    }
  };

  const getFilteredAnnouncements = () => {
    let filtered = announcements;

    if (activeTab === 1) {
      filtered = filtered.filter((ann) => ann.status === "ongoing");
    } else if (activeTab === 2) {
      filtered = filtered.filter((ann) => ann.status === "upcoming");
    } else if (activeTab === 3) {
      // For expired tab:
      // - Show all non-pinned expired announcements
      // - Show pinned announcements ONLY if they were forcefully moved to expired (manually_expired = true)
      // - Don't show pinned announcements that naturally expired
      filtered = filtered.filter((ann) => {
        if (ann.status === "expired") {
          const isPinned = ann.pinned || ann.isPinned;
          const wasManuallyExpired =
            ann.manuallyExpired || ann.manually_expired;

          if (isPinned) {
            // Only show pinned announcements if they were forcefully moved to expired
            return wasManuallyExpired;
          } else {
            // Show all non-pinned expired announcements
            return true;
          }
        }
        return false;
      });
    }

    if (searchTerm) {
      filtered = filtered.filter(
        (ann) =>
          ann.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          ann.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
          ann.author.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (selectedDate) {
      filtered = filtered.filter((ann) => {
        if (!ann.startDate || !ann.endDate) return false;
        const selectedDateObj = new Date(selectedDate);
        selectedDateObj.setHours(0, 0, 0, 0);
        const announcementStartDate = new Date(ann.startDate);
        announcementStartDate.setHours(0, 0, 0, 0);
        const announcementEndDate = new Date(ann.endDate);
        announcementEndDate.setHours(0, 0, 0, 0);
        return (
          selectedDateObj >= announcementStartDate &&
          selectedDateObj <= announcementEndDate
        );
      });
    }

    if (selectedPriority.length > 0) {
      filtered = filtered.filter(
        (ann) =>
          ann.priority &&
          selectedPriority.some(
            (priority) => ann.priority.toLowerCase() === priority.toLowerCase()
          )
      );
    }

    if (selectedLabel.length > 0) {
      filtered = filtered.filter((ann) => {
        if (!ann.label) return false;
        // Split the announcement's label by comma and check if any matches selected labels
        const announcementLabels = ann.label
          .split(",")
          .map((l) => l.trim().toLowerCase());
        return selectedLabel.some((selectedLbl) =>
          announcementLabels.includes(selectedLbl.toLowerCase())
        );
      });
    }

    if (myPostChecked) {
      const member = localStorage.getItem("member");
      let currentUserName = null;
      if (member) {
        try {
          const currentUser = JSON.parse(member);
          currentUserName =
            currentUser.full_name || currentUser.fullName || currentUser.name;
        } catch (error) {
          console.error("Error parsing member data:", error);
        }
      }
      if (currentUserName) {
        filtered = filtered.filter(
          (ann) =>
            ann.creatorName &&
            ann.creatorName.toLowerCase() === currentUserName.toLowerCase()
        );
      } else {
        filtered = [];
      }
    }

    return pinPost.sortAnnouncementsWithPinned(filtered);
  };

  const handleCreateAnnouncement = () => {
    navigate("/create-announcement", {
      state: { sourceTab: activeTab }
    });
  };

  const handleMoveToExpired = (announcementId) => {
    setAnnouncementToMoveToExpired(announcementId);
    setShowMoveToExpiredConfirmation(true);
  };

  const confirmMoveToExpired = async () => {
    try {
      await moveAnnouncementToExpiredRedux(announcementToMoveToExpired);
      loadAnnouncements();
      setShowMoveToExpiredConfirmation(false);
      setAnnouncementToMoveToExpired(null);
      setMoveToExpiredSuccessMessage(
        "Announcement has been successfully moved to expired!"
      );
      setShowMoveToExpiredSuccess(true);
    } catch (error) {
      console.error("Error moving announcement to expired:", error);
      setShowMoveToExpiredConfirmation(false);
      setAnnouncementToMoveToExpired(null);
    }
  };

  const cancelMoveToExpired = () => {
    setShowMoveToExpiredConfirmation(false);
    setAnnouncementToMoveToExpired(null);
  };

  const handleEditAnnouncement = (announcementId) => {
    navigate(`/edit-announcement/${announcementId}`, {
      state: {
        sourceTab: activeTab,
        announcementId: announcementId
      }
    });
  };

  const handleAnnouncementHistory = async (announcementId) => {
    try {
      await loadAnnouncementRedux(announcementId);
      const announcement = announcements.find(
        (ann) => ann.id === announcementId
      );
      if (announcement) {
        setSelectedAnnouncementForHistory(announcement);
        setShowHistoryModal(true);
      }
    } catch (error) {
      console.error("Error fetching announcement for history:", error);
      const announcement = announcements.find(
        (ann) => ann.id === announcementId
      );
      if (announcement) {
        setSelectedAnnouncementForHistory(announcement);
        setShowHistoryModal(true);
      }
    }
  };

  const handleReminder = (announcementId) => {
    console.log("Set reminder for announcement:", announcementId);
  };

  const handlePinPost = pinPost.handlePinPost;

  const handleDirectCommunication = (announcementId) => {
    console.log("Start direct communication for announcement:", announcementId);
  };

  const handleDeleteAnnouncement = (announcementId) => {
    setAnnouncementToDelete(announcementId);
    setShowDeleteConfirmation(true);
  };

  const handleConfirmDelete = async () => {
    if (announcementToDelete) {
      try {
        await removeAnnouncementRedux(announcementToDelete);
      } catch (error) {
        console.error("Error deleting announcement:", error);
      }
    }
    setShowDeleteConfirmation(false);
    setAnnouncementToDelete(null);
  };

  const handleCancelDelete = () => {
    setShowDeleteConfirmation(false);
    setAnnouncementToDelete(null);
  };

  const handleRestoreAnnouncement = (announcementId) => {
    setAnnouncementToRestore(announcementId);
    setShowRestoreConfirmation(true);
  };

  const confirmRestoreAnnouncement = async () => {
    try {
      await restoreExpiredAnnouncementRedux(announcementToRestore);
      loadAnnouncements();
      setShowRestoreConfirmation(false);
      setAnnouncementToRestore(null);
      setRestoreSuccessMessage("Announcement has been successfully restored!");
      setShowRestoreSuccess(true);
    } catch (error) {
      console.error("Error restoring announcement:", error);
      setShowRestoreConfirmation(false);
      setAnnouncementToRestore(null);
    }
  };

  const cancelRestoreAnnouncement = () => {
    setShowRestoreConfirmation(false);
    setAnnouncementToRestore(null);
  };

  const handleClearSuccessMessage = () => {
    clearAllSuccessRedux();
  };

  const isImage = (fileName) => {
    const extension = fileName?.split(".").pop()?.toLowerCase();
    return ["jpg", "jpeg", "png", "gif", "bmp", "webp"].includes(extension);
  };

  const handleImageClick = (attachment, announcement) => {
    const allAttachments = announcement.attachments || [];
    const imageAttachments = allAttachments.filter(
      (att) =>
        isImage(att.file_name || att.name) ||
        (!att.file_name && !att.name && !isDocument(att.file_name || att.name))
    );
    const clickedIndex = imageAttachments.findIndex(
      (img) =>
        (img.file_url || img.url || img) ===
        (attachment.file_url || attachment.url || attachment)
    );
    const formattedImages = imageAttachments.map((img, index) => ({
      src: img.file_url || img.url || img,
      alt: img.file_name || img.name || `Image ${index + 1}`,
      name: img.file_name || img.name || `image-${index + 1}`
    }));
    setSelectedImages(formattedImages);
    setSelectedImageIndex(clickedIndex >= 0 ? clickedIndex : 0);
    setIsImageSliderOpen(true);
  };

  const handleImageSliderClose = () => {
    setIsImageSliderOpen(false);
    setSelectedImages([]);
    setSelectedImageIndex(0);
  };

  const handleDocumentClick = (attachment) => {
    setSelectedDocument(attachment);
    setIsDocumentViewerOpen(true);
  };

  const handleDocumentClose = () => {
    setIsDocumentViewerOpen(false);
    setSelectedDocument(null);
  };

  const getFileIcon = (fileName) => {
    const extension = fileName?.split(".").pop()?.toLowerCase();
    switch (extension) {
      case "pdf":
        return <FaFilePdf className="w-6 h-6 text-black font-bold" />;
      case "doc":
      case "docx":
        return <FaFileWord className="w-6 h-6 text-blue-500" />;
      default:
        return <FaImage className="w-6 h-6 text-gray-400" />;
    }
  };

  const isDocument = (fileName) => {
    const extension = fileName?.split(".").pop()?.toLowerCase();
    return ["pdf", "doc", "docx"].includes(extension);
  };

  const handleHistoryModalClose = () => {
    setShowHistoryModal(false);
    setSelectedAnnouncementForHistory(null);
  };

  const handleDropdownToggle = (announcementId) => {
    setOpenDropdownId(
      openDropdownId === announcementId ? null : announcementId
    );
  };

  const handleFilterToggle = () => {
    setIsFilterExpanded(!isFilterExpanded);
  };

  const handleTabChange = (tabNumber) => {
    setActiveTab(tabNumber);
    localStorage.setItem("announcementActiveTab", tabNumber.toString());
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      {/* Header */}
      <div className="mb-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-semibold text-gray-900">
              Announcements List
            </h1>

            <div className="flex items-center space-x-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={myPostChecked}
                  onChange={(e) => setMyPostChecked(e.target.checked)}
                  className="form-checkbox h-4 w-4 text-primary rounded border-gray-300 focus:ring-primary"
                />
                <span className="ml-2 text-sm text-primary">My Post</span>
              </label>

              <button
                onClick={handleFilterToggle}
                className={`flex items-center justify-center  px-4 py-2 rounded-lg transition-colors text-sm font-medium w-[99px] h-[48px] ${
                  isFilterExpanded
                    ? "bg-primary text-white"
                    : "bg-white text-primary border border-primary hover:bg-gray-50"
                }`}
              >
                <BiFilter className="mr-2 w-4 h-4" />
                Filter
              </button>

              <button
                onClick={handleCreateAnnouncement}
                className="flex items-center justify-center bg-primary text-white px-4 py-2 rounded-lg transition-colors text-sm font-medium w-[217px] h-[48px]"
              >
                <FaPlus className="mr-2 w-4 h-4" />
                Create Announcements
              </button>
            </div>
          </div>

          {isFilterExpanded && (
            <div className="flex items-center justify-end space-x-6 ">
              <div className="min-w-[160px]">
                <Calendar
                  value={selectedDate}
                  onChange={setSelectedDate}
                  placeholder="Select Date"
                />
              </div>

              <div className="min-w-[160px] ">
                <FilterSelectModal
                  placeholder="Select Priority"
                  options={[
                    { value: "Urgent", label: "Urgent" },
                    { value: "High", label: "High" },
                    { value: "Normal", label: "Normal" },
                    { value: "Low", label: "Low" }
                  ]}
                  value={selectedPriority}
                  onApply={setSelectedPriority}
                  className="w-full"
                />
              </div>

              <div className="min-w-[160px]">
                <FilterSelectModal
                  placeholder="Select Label"
                  options={availableLabels.map((label) => ({
                    value: label,
                    label: label
                  }))}
                  value={selectedLabel}
                  onApply={setSelectedLabel}
                  className="w-full"
                />
              </div>

              <div className="relative min-w-[200px] max-w-[250px]">
                <BiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search list..."
                  className="w-full h-[42px] pl-10 pr-4 border border-primary rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm text-primary placeholder-primary"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="rounded-[27px] bg-white">
        <div className="p-4">
          <div className="flex mb-4 bg-[#3C9D9B1A] rounded">
            <button
              className={`flex-1 w-full py-2 font-[600] text-[#090909] ${
                activeTab === 1
                  ? "border border-primary"
                  : "border border-transparent"
              }`}
              onClick={() => handleTabChange(1)}
            >
              Ongoing
            </button>
            <button
              className={`flex-1 w-full py-2 font-[600] text-[#090909] ${
                activeTab === 2
                  ? "border border-primary"
                  : "border border-transparent"
              }`}
              onClick={() => handleTabChange(2)}
            >
              Upcoming
            </button>
            <button
              className={`flex-1 w-full py-2 font-[600] text-[#090909] ${
                activeTab === 3
                  ? "border border-primary"
                  : "border border-transparent"
              }`}
              onClick={() => handleTabChange(3)}
            >
              Expired
            </button>
          </div>

          {/* Announcements List */}
          {getFilteredAnnouncements().length === 0 ? (
            <div className="flex flex-col justify-center items-center min-h-[300px] text-center w-full">
              <h3 className="text-xl font-semibold text-gray-900">
                No announcements found
              </h3>
            </div>
          ) : (
            <AnnouncementListPreview
              announcements={getFilteredAnnouncements()}
              loading={loading}
              openDropdownId={openDropdownId}
              dropdownRef={dropdownRef}
              currentTab={activeTab}
              handleDropdownToggle={handleDropdownToggle}
              handleEditAnnouncement={handleEditAnnouncement}
              handleAnnouncementHistory={handleAnnouncementHistory}
              handleMoveToExpired={handleMoveToExpired}
              handleReminder={handleReminder}
              handlePinPost={handlePinPost}
              handlePinIconClick={pinPost.handlePinIconClick}
              handleDirectCommunication={handleDirectCommunication}
              handleDeleteAnnouncement={handleDeleteAnnouncement}
              handleRestoreAnnouncement={handleRestoreAnnouncement}
              handleImageClick={handleImageClick}
              handleDocumentClick={handleDocumentClick}
              isDocument={isDocument}
              getFileIcon={getFileIcon}
            />
          )}
        </div>
      </div>

      <ImageSlider
        isOpen={isImageSliderOpen}
        onClose={handleImageSliderClose}
        images={selectedImages}
        initialIndex={selectedImageIndex}
      />

      {showDeleteConfirmation && (
        <ConfirmationMessageBox
          message="Are you sure you want to delete this announcement? This action cannot be undone."
          onConfirm={handleConfirmDelete}
          onCancel={handleCancelDelete}
        />
      )}

      {showRestoreConfirmation && (
        <ConfirmationMessageBox
          message="Are you sure you want to restore this announcement?"
          onConfirm={confirmRestoreAnnouncement}
          onCancel={cancelRestoreAnnouncement}
        />
      )}

      {showMoveToExpiredConfirmation && (
        <ConfirmationMessageBox
          message="Are you sure you want to move this announcement to expired?"
          onConfirm={confirmMoveToExpired}
          onCancel={cancelMoveToExpired}
        />
      )}

      {showMoveToExpiredSuccess && (
        <MessageBox
          message={moveToExpiredSuccessMessage}
          clearMessage={() => setShowMoveToExpiredSuccess(false)}
        />
      )}

      {showRestoreSuccess && (
        <MessageBox
          message={restoreSuccessMessage}
          clearMessage={() => setShowRestoreSuccess(false)}
        />
      )}

      {deleteSuccess && message && (
        <MessageBox
          message={message}
          clearMessage={handleClearSuccessMessage}
        />
      )}

      <AnnouncementHistoryModal
        isOpen={showHistoryModal}
        onClose={handleHistoryModalClose}
        announcement={selectedAnnouncementForHistory}
        currentUser={localStorage.getItem("currentUser") || "Current User"}
      />

      {isDocumentViewerOpen && selectedDocument && (
        <DocumentViewer
          fileUrl={
            selectedDocument.file_url ||
            selectedDocument.url ||
            selectedDocument.base64
          }
          fileName={selectedDocument.file_name || selectedDocument.name}
          onClose={handleDocumentClose}
        />
      )}
    </div>
  );
};

export default AnnouncementList;
