import React from "react";
import { Link } from "react-router-dom";
// import { Info } from "Components/Ui/Info";
import DottedNidBox from "Components/ImageBox/DottedNidBox";
import { GrDownload } from "react-icons/gr";
import edit2 from "../../../assets/edit-02.png";
import { handleDownload } from "../../../utils/handleDownload";
import axios from "axios";
import Info from "../../../Components/Ui/Info";
import DynamicEditLink from "./DynamicLinkEditButton";

// Axios instance for base API URL
const api = axios.create({
  baseURL: import.meta.env.VITE_BASE_API
});

/**
 * ProfileInformation Component
 * Extracted first-tab UI for displaying member profile details.
 *
 * @param {Object} props
 * @param {Object} props.memberData - Member data object
 * @param {Function} props.onEditClick - Handler for edit button click
 */
const ProfileInformationView = ({ memberData = {}, onEditClick }) => {
  // Destructure relevant fields with fallbacks
  const {
    id,
    full_name,
    general_contact,
    permanent_address,
    gender,
    occupation,
    religion,
    general_email,
    nid_number,
    present_address,
    date_of_birth,
    marital_status,
    nid_front,
    nid_back
  } = memberData;

  // Helper for downloading files
  const downloadFile = (path, filename) => {
    handleDownload(`${api.defaults.baseURL}${path}`, filename);
  };

  return (
    <div className="mx-auto border rounded-lg p-3 ">
      <div className="flex justify-between mb-4">
        <h2 className="text-lg font-bold text-primary">Profile Information</h2>
        <DynamicEditLink basePath="general-information-edit" resourceId={id} />
      </div>
      <div className="grid grid-cols-3 gap-4">
        <div className="space-y-2">
          <Info label="Full Name">{full_name || "--"}</Info>
          <Info label="Contact Number">{general_contact || "--"}</Info>
          <Info label="Permanent Address">{permanent_address || "--"}</Info>
          <Info label="Gender">{gender || "--"}</Info>
          <Info label="Occupation">{occupation || "--"}</Info>
          <Info label="Religion">{religion || "--"}</Info>
        </div>

        <div className="space-y-2">
          <Info label="E-Mail">{general_email || "--"}</Info>
          <Info label="NID Number">{nid_number || "--"}</Info>
          <Info label="Present Address">{present_address || "--"}</Info>
          <Info label="Date Of Birth">{date_of_birth || "--"}</Info>
          <Info label="Marital Status">{marital_status || "--"}</Info>
        </div>

        <div className="pt-5 space-y-4 flex flex-col items-end">
          {/* NID Front */}
          <div className="py-2 flex flex-col items-end ">
            {nid_front ? (
              <>
                <button
                  className="top-0 right-0 cursor-pointer bg-white p-1 rounded-full shadow-md hover:bg-gray-200 transition"
                  onClick={() => downloadFile(nid_front, "nid_front_image.jpg")}
                >
                  <GrDownload className="text-primary font-bold" />
                </button>
                <img
                  src={`${api.defaults.baseURL}${nid_front}`}
                  alt="NID Front"
                  className="rounded border object-cover w-24 h-24 "
                />
              </>
            ) : (
              <DottedNidBox title="NID Front" />
            )}
          </div>

          {/* NID Back */}
          <div className="py-2 flex flex-col items-end ">
            {nid_back ? (
              <>
                <button
                  className="top-0 right-0 cursor-pointer bg-white p-1 rounded-full shadow-md hover:bg-gray-200 transition"
                  onClick={() => downloadFile(nid_back, "nid_back_image.jpg")}
                >
                  <GrDownload className="text-primary font-bold" />
                </button>
                <img
                  src={`${api.defaults.baseURL}${nid_back}`}
                  alt="NID Back"
                  className="rounded border object-cover w-24 h-24 "
                />
              </>
            ) : (
              <DottedNidBox title="NID Back" />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileInformationView;
