import React from "react";
import { FaEye } from "react-icons/fa";
import { Link } from "react-router-dom";
import user1 from "../../../assets/user/user.png";
import { setActiveTabs } from "../../../redux/slices/memberSlice";
import { useDispatch } from "react-redux";
import TableCellText from "../../../Components/Table/TableCellText";
import TableCell from "../../../Components/Table/TableCell";
import NoData from "../../../Components/Table/NoData";

const MemberListTable = ({ member, error }) => {
  const dispatch = useDispatch();
  const hasMembers = Array.isArray(member) && member.length > 0;

  return (
    <div className="w-full">
      <div className="relative overflow-x-auto max-h-[70vh] overflow-y-auto  bg-white">
        <table className="w-full text-sm text-left rtl:text-right">
          {/* <thead className="bg-primary shadow-lg border-b border-subprimary sticky top-0"> */}
          <thead className="bg-subprimary  border-b border-subprimary sticky top-0 z-10">
            <tr>
              <th className="px-3 font-[700] py-2 text-base text-left">Name</th>
              <th className="px-3 font-[700] py-2 text-base text-left">
                Contact
              </th>
              <th className="px-3 font-[700] py-2 text-base text-left">
                Email
              </th>
              <th className="px-3 font-[700] py-2 text-base text-left">Type</th>
              <th className="px-3 font-[700] py-2 text-base text-left">Role</th>
              <th className="px-3 font-[700] py-2 text-base text-left">
                Status
              </th>
              <th className="px-3 font-[700] py-2 text-base text-center">
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            {!hasMembers ? (
              <tr>
                <td colSpan="7">
                  <NoData
                    message={
                      error?.message === "No results found"
                        ? "No results found"
                        : "No members available"
                    }
                  />
                </td>
              </tr>
            ) : (
              member?.map((member, index) => (
                <tr key={index} className="bg-white border-b hover:bg-gray-50">
                  <th
                    scope="row"
                    className="px-3 py-3 font-medium flex items-center"
                  >
                    <img
                      src={member.photo ? member.photo : user1}
                      alt="User"
                      className="w-6 h-6 mr-2 rounded-full"
                    />
                    {member.full_name}
                  </th>
                  <td className="px-3 py-2 text-base text-left">
                    {member.general_contact}
                  </td>
                  <td className="px-3 py-2 text-base text-left">
                    {member.general_email}
                  </td>
                  <td className="px-3 py-2 text-base text-left">
                    {member.member_type_name}
                  </td>
                  {/* <td className="px-3 py-2 text-base text-left">
                    {member?.member_roles
                      ?.map((role) => role.role_name)
                      .join(", ")}
                  </td> */}
                  {/* <td className="px-3 py-2 text-base text-left">
                    {member?.member_roles
                      ?.map((role) => role.role_name)
                      .join(", ")}
                  </td> */}

                  <td
                    className="px-3 py-2 text-base text-left"
                    title={member?.member_roles
                      ?.map((role) => role.role_name)
                      .join(", ")}
                  >
                  {(() => {
                    const uniqueRoles = [
                      ...new Map(member?.member_roles?.map(role => [role.role_name, role])).values()
                    ];

                    return uniqueRoles.length > 3
                      ? `${uniqueRoles
                          .slice(0, 3)
                          .map(role => role.role_name)
                          .join(", ")}, ...`
                      : uniqueRoles.map(role => role.role_name).join(", ");
                  })()}

                  </td>

                  <TableCell>
                    <button
                      className={`mx-2 py-2 px-2 rounded-8 text-white ${
                        Number(member.is_org_member) === 1
                          ? "bg-primary"
                          : "bg-secondary"
                      }`}
                    >
                      {Number(member.is_org_member) === 1
                        ? "Active"
                        : "Inactive"}
                    </button>
                  </TableCell>
                  <TableCell>
                    <div className="flex justify-center">
                      <Link
                        to={{
                          pathname: `/member-profile/${member.id}`,
                          state: { from: location.pathname }
                        }}
                        onClick={() => dispatch(setActiveTabs(1))}
                      >
                        <FaEye className="w-[25px] h-[20px] text-primary" />
                      </Link>
                    </div>
                  </TableCell>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default MemberListTable;
