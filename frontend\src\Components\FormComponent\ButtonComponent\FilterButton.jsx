import React from "react";
import { RiFilter3Fill } from "react-icons/ri";

const FilterButton = ({ active, onClick, children }) => {
  return (
    <button
      type="button"
      onClick={onClick}
      className={`flex items-center px-4 py-[11px] rounded-8 border transition-all duration-300 
        ${active ? "bg-primary border-primary text-white" : "bg-white border-primary text-primary"}
      `}
    >
      <RiFilter3Fill
        className={`mr-2 text-[24px] ${active ? "text-white" : "text-primary"}`}
        size={20}
      />
      <span className="font-lg">{children}</span>
    </button>
  );
};

export default FilterButton;
