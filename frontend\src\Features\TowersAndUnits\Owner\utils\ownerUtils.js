import * as yup from "yup";

/**
 * Returns ordinal suffix (e.g., 1st, 2nd, 3rd).
 */
export const getOrdinal = (n) => {
  const s = ["th", "st", "nd", "rd"];
  const v = n % 100;
  return n + (s[(v - 20) % 10] || s[v] || s[0]);
};

/**
 * Formats a date string to 'dd-MMM-yyyy' (e.g., 29-Apr-2025).
 */
export const formatDate = (dateStr) =>
  new Date(dateStr)
    .toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "short",
      year: "numeric"
    })
    .replace(/ /g, "-");

/**
 * Owner validation schema using yup for react-hook-form.
 */
export const ownerValidationSchema = yup.object().shape({
  owners: yup.array().of(
    yup.object().shape({
      memberId: yup
        .mixed()
        .required("Unit Owner Name is required")
        .test(
          "is-valid-member",
          "Please select a valid Unit Owner Name",
          function (value) {
            return value !== null && value !== undefined && value !== "";
          }
        ),

      ownershipPercentage: yup
        .number()
        .typeError("Ownership percentage must be a number")
        .required("Ownership percentage is required")
        .min(0, "Ownership percentage cannot be negative")
        .max(100, "Ownership percentage cannot exceed 100")
        .test(
          "decimal-places",
          "Ownership percentage cannot have more than 2 decimal places",
          (value) => {
            if (!value) return true;
            return /^\d+(\.\d{0,2})?$/.test(value.toString());
          }
        ),

      dateofOwnership: yup
        .date()
        .typeError("Invalid date")
        .required("Date of Ownership is required")
        .max(new Date(), "Ownership date cannot be in the future")
        .transform((value, originalValue) =>
          originalValue === "" ? null : value
        ),

      document: yup
        .array()
        .nullable()
        .default([])
        .test(
          "max-files",
          "You can upload a maximum of 5 documents.",
          (files) => !files || files.length <= 5
        ),

      docLinks: yup.array().nullable().default([]),

      ownershipTransferFromId: yup
        .number()
        .nullable()
        .test(
          "is-valid-transfer",
          "Please select a valid owner to transfer ownership from",
          function (value) {
            // If no transfer is selected, that's fine
            if (!value) return true;

            // Get the current owner's ID
            const currentOwnerId = this.parent.memberId;

            // Don't allow self-transfer
            if (value === currentOwnerId) {
              return this.createError({
                message: "Cannot transfer ownership to yourself"
              });
            }

            return true;
          }
        )
        .transform((value, originalValue) =>
          String(originalValue).trim() === "" ? null : value
        )
    })
  )
  // .test(
  //   "unique-memberId",
  //   "Duplicate Unit Owner Names are not allowed. Please remove duplicate entries.",
  //   function (owners) {
  //     if (!owners) return true;
  //     const memberIds = owners.map((o) => o.memberId).filter(Boolean);
  //     const uniqueMemberIds = new Set(memberIds);
  //     return uniqueMemberIds.size === memberIds.length;
  //   }
  // )
});

/**
 * Owner edit validation schema for single owner editing.
 */
export const ownerEditValidationSchema = yup.object().shape({
  memberId: yup
    .mixed()
    .required("Unit Owner Name is required")
    .test(
      "is-valid-member",
      "Please select a valid Unit Owner Name",
      function (value) {
        return value !== null && value !== undefined && value !== "";
      }
    ),

  ownershipPercentage: yup
    .number()
    .typeError("Ownership percentage must be a number")
    .required("Ownership percentage is required")
    .min(0, "Ownership percentage cannot be negative")
    .max(100, "Ownership percentage cannot exceed 100")
    .test(
      "decimal-places",
      "Ownership percentage cannot have more than 2 decimal places",
      (value) => {
        if (!value) return true;
        return /^\d+(\.\d{0,2})?$/.test(value.toString());
      }
    ),

  dateofOwnership: yup
    .date()
    .typeError("Invalid date")
    .required("Date of Ownership is required")
    .max(new Date(), "Ownership date cannot be in the future")
    .transform((value, originalValue) => (originalValue === "" ? null : value)),

  document: yup
    .array()
    .nullable()
    .default([])
    .test(
      "max-files",
      "You can upload a maximum of 5 documents.",
      (files) => !files || files.length <= 5
    )
});
