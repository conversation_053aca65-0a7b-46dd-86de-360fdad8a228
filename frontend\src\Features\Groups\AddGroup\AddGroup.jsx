import { useEffect, useCallback, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams, useNavigate } from "react-router-dom";
import TextInputComponent from "Components/FormComponent/TextInputComponent";
import TextareaComponent from "Components/FormComponent/TextareaComponent";
import CheckboxComponent from "Components/FormComponent/CheckboxComponent";
import { BiArrowBack } from "react-icons/bi";
import MemberGroupAsignTable from "Components/Table/Group/MemberGroupAsignTable";
import MessageBox from "Components/MessageBox/MessageBox";
import {
  createGroup,
  updateGroup,
  fetchGroupDetail,
  fetchRoles,
  clearGroupMessage,
  setGroupError,
  setSelectedMemberIds,
  updateGroupFormField,
  setGroupFormData,
  clearGroupFormData,
  toggleGroupStatus
} from "../../../redux/slices/groups/groupSlice";
import { Div } from "Components/Ui/Div";
import { Paragraph } from "Components/Ui/Paragraph";
import { Heading } from "Components/Ui/Heading";
import SubmitButton from "Components/FormComponent/ButtonComponent/SubmitButton";
import { groupFields } from "../../../utils/formFields";
import ArrowHeading from "../../../Components/HeadingComponent/ArrowHeading";
import { checkPermission } from "../../../utils/permissionUtils";
import TableSkeleton from "../../../Components/Loaders/TableSkeleton";

import Button from "Components/FormComponent/ButtonComponent/Button";
import ConfirmationMessageBox from "Components/MessageBox/ConfirmationMessageBox";

import isEqual from "lodash/isEqual";
import { updateChangedFields } from "../../../utils/updateFileChange";

const AddGroup = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  // FIX: Add groupDetail to the selector
  const { roles, error, message, groupFormData, groupDetail } = useSelector(
    (state) => state.group
  );

  const [initialPayload, setInitialPayload] = useState(null);
  const [hasPermission, setHasPermission] = useState(false);
  const [loadingPermission, setLoadingPermission] = useState(true);
  const [showStatusConfirm, setShowStatusConfirm] = useState(false);
  const [isFormChangedFirstTab, setIsFormChangedFirstTab] = useState({});
  const [buttonDisableFirst, setButtonDisableFirst] = useState(false);
  const [updatedRoleIds, setUpdatedRoleIds] = useState([]);
  const [hasChanges, setHasChanges] = useState(false); 

  // FIX: Get is_active from groupDetail
  const is_active = groupDetail?.is_active ?? false;
  useEffect(() => {
  
    setHasChanges(!isEqual(isFormChangedFirstTab, {})); 
  }, [isFormChangedFirstTab]);
  // Check permission when the component mounts.
  useEffect(() => {
    const fetchPermission = async () => {
      const permissionId = id ? 8 : 7;
      const permissionGranted = await checkPermission("org", permissionId);
      setHasPermission(permissionGranted);
      setLoadingPermission(false);
    };
    fetchPermission();
  }, [id]);

  // Redirect after permission check
  useEffect(() => {
    if (!loadingPermission && !hasPermission) {
      navigate("/not-authorized");
    }
  }, [loadingPermission, hasPermission, navigate]);

  // Clear messages
  useEffect(() => {
    dispatch(clearGroupMessage());
  }, [dispatch]);

  // Fetch roles
  useEffect(() => {
    dispatch(fetchRoles());
  }, [dispatch]);

  // Clear form data
  useEffect(() => {
    if (!id) {
      dispatch(clearGroupFormData());
      dispatch(setSelectedMemberIds([]));
    }
    return () => {
      dispatch(clearGroupFormData());
      dispatch(setSelectedMemberIds([]));
    };
  }, [id, dispatch]);

  // Fetch group details for edit mode
  useEffect(() => {
    if (id) {
      dispatch(fetchGroupDetail(id)).then((action) => {
        if (action.payload) {
          const {
            group_name,
            group_description,
            roles: groupRoles,
            members,
            is_active // FIX: Ensure this is included
          } = action.payload;
          const role_ids = groupRoles.map((role) => role.id);
          const member_ids = members.map((member) => member.id);

          const initialData = {
            group_name,
            group_description,
            role_ids,
            member_ids,
            is_active // FIX: Include status in initial data
          };
          setInitialPayload(initialData);

          dispatch(
            setGroupFormData({
              group_name,
              group_description,
              role_ids,
              member_ids,
              selectAll: false
            })
          );
          dispatch(setSelectedMemberIds(member_ids));
        }
      });
    }
  }, [id, dispatch]);

  // Update selectAll flag
  useEffect(() => {
    if (id && roles && roles.length > 0) {
      dispatch(
        updateGroupFormField({
          field: "selectAll",
          value: groupFormData.role_ids.length === roles.length
        })
      );
    }
  }, [roles, groupFormData.role_ids, id, dispatch]);

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    dispatch(updateGroupFormField({ field: name, value }));
    updateChangedFields(setIsFormChangedFirstTab, name, value);
    if (name === "group_name" && error === "Group name is required.") {
      dispatch(clearGroupMessage());
    }
  };

  // Handle role checkbox changes
  const handleRoleChange = (e) => {
    const { name, value, checked } = e.target;
    const roleId = parseInt(value, 10);

    setUpdatedRoleIds((prev) => {
      let updated;
      if (checked) {
        updated = prev.includes(roleId) ? prev : [...prev, roleId];
      } else {
        updated = prev.filter((id) => id !== roleId);
      }
      updateChangedFields(setIsFormChangedFirstTab, name, updated);
      return updated;
    });

    if (checked) {
      dispatch(
        updateGroupFormField({
          field: "role_ids",
          value: [...groupFormData.role_ids, roleId]
        })
      );
    } else {
      dispatch(
        updateGroupFormField({
          field: "role_ids",
          value: groupFormData.role_ids.filter((id) => id !== roleId)
        })
      );
      dispatch(
        updateGroupFormField({
          field: "selectAll",
          value: false
        })
      );
    }
  };

  // Handle select all
  const handleSelectAll = (e) => {
    const { name, checked } = e.target;
    updateChangedFields(
      setIsFormChangedFirstTab,
      name,
      checked ? roles.map((role) => role.id) : []
    );
    dispatch(
      updateGroupFormField({
        field: "selectAll",
        value: checked
      })
    );
    dispatch(
      updateGroupFormField({
        field: "role_ids",
        value: checked ? roles.map((role) => role.id) : []
      })
    );
  };

  // Ensure role_ids are set when selectAll is checked
  useEffect(() => {
    if (groupFormData.selectAll && roles && roles.length > 0) {
      dispatch(
        updateGroupFormField({
          field: "role_ids",
          value: roles.map((role) => role.id)
        })
      );
    }
  }, [roles, groupFormData.selectAll, dispatch]);

  // Handle member selection changes
  const handleMemberSelectionChange = useCallback(
    (selectedMemberIds) => {
      const filtered = selectedMemberIds.filter(
        (id) => id !== undefined && id !== null
      );
      dispatch(updateGroupFormField({ field: "member_ids", value: filtered }));
    },
    [dispatch]
  );

  const clearMessage = () => {
    dispatch(clearGroupMessage());
  };

  // Form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

  if ( !hasChanges) return;

    if (!groupFormData.group_name.trim()) {
      dispatch(setGroupError("Group name is required."));
      return;
    }

    const payload = {
      group_name: groupFormData.group_name,
      group_description: groupFormData.group_description,
      role_ids: groupFormData.role_ids,
      member_ids: groupFormData.member_ids,
      is_group: true
    };

    if (
      id &&
      initialPayload &&
      JSON.stringify(payload) === JSON.stringify(initialPayload)
    ) {
      console.log("No changes made. Update aborted.");
      return;
    }

    try {
      if (id) {
        dispatch(updateGroup({ id, payload }));
      } else {
        dispatch(createGroup(payload));
      }
    } catch (err) {
      console.error("Error submitting form:", err);
    }
  };

  // Handle OK in message box
  const handleOk = () => {
    if (id) {
      navigate(`/groupProfile/${id}`);
    } else {
      navigate("/group-list");
    }
  };

  useEffect(() => {
    setButtonDisableFirst(isEqual(isFormChangedFirstTab, {}));
  }, [isFormChangedFirstTab]);

  if (loadingPermission) {
    return <TableSkeleton />;
  }

  const handleToggleStatus = () => {
    setShowStatusConfirm(true);
  };

  const handleConfirmToggleStatus = () => {
    setShowStatusConfirm(false);
    dispatch(toggleGroupStatus(id));
  };



  

  return (
    <Div className="h-full">
      <Div className="container">
        <form onSubmit={handleSubmit}>
          <Div className="md:flex justify-between py-2">
            <ArrowHeading
              title={`${id ? "Edit Group" : "Add Group"}`}
              size="xl"
              color="black"
              onNext={() => navigate(-1)}
              fontWeight="semibold"
            />
            <div className="flex ">
              {id && (
                <button
                  type="button"
                  onClick={handleToggleStatus}
                  className={`px-4 py-2 my-3 mr-2 font-semibold rounded transition-all duration-200 ${
                    is_active
                      ? "border-primary bg-primary text-white"
                      : "border-error bg-error text-white"
                  }`}
                >
                  <span className="px-1 text-base">
                    {is_active ? "Active" : "Inactive"}
                  </span>
                </button>
              )}

              {/* <SubmitButton
                text={id ? "Update" : "Create"}
                loading={false}
                disabled={false}
                onClick={handleSubmit}
                width="auto"
                isFormChange={buttonDisableFirst}
              /> */}
            <SubmitButton
        text={id ? "Update" : "Create"}
        loading={false}
        disabled={!hasChanges} 
        onClick={handleSubmit}
        width="auto"
      />
            </div>
          </Div>
          <Div className="bg-white border p-3 pt-2 rounded-27 mx-auto">
            <Div className="mb-[24px]">
              <TextInputComponent
                {...groupFields.group_name}
                name="group_name"
                value={groupFormData.group_name}
                onChange={handleInputChange}
                required
                error={error === "Group name is required." ? error : ""}
              />
            </Div>
            <Div className="flex justify-center gap-4">
              <Div className="w-[40%]">
                <Heading level={1} className="my-2 text-base font-medium">
                  Roles
                </Heading>
                <Div className="max-h-[300px] overflow-y-auto border w-full border-BorderSecondary p-2 rounded-md shadow-sm">
                  <Div className="mb-2">
                    <CheckboxComponent
                      {...groupFields.selectAll}
                      checked={groupFormData.selectAll}
                      onChange={handleSelectAll}
                      value="all"
                    />
                  </Div>
                  {roles.map((role) => (
                    <Div key={role.id} className="mb-2">
                      <CheckboxComponent
                        name="role_ids"
                        label={role.role_name}
                        checked={groupFormData.role_ids.includes(role.id)}
                        onChange={handleRoleChange}
                        value={role.id}
                      />
                    </Div>
                  ))}
                </Div>
              </Div>
              <Div className="w-[60%]">
                <TextareaComponent
                  {...groupFields.group_description}
                  name="group_description"
                  value={groupFormData.group_description}
                  onChange={handleInputChange}
                />
              </Div>
            </Div>
            <Div className="p-1.5">
              <MemberGroupAsignTable
                onSelectionChange={handleMemberSelectionChange}
                selectedMemberIds={groupFormData.member_ids}
                setIsFormChangedFirstTab={setIsFormChangedFirstTab}
                updateChangedFields={updateChangedFields}
                groupId={id}
              />
            </Div>
          </Div>
        </form>
      </Div>
      {((error && error !== "Group name is required.") || message) && (
        <MessageBox
          message={message}
          error={error && error !== "Group name is required." ? error : null}
          clearMessage={clearMessage}
          onOk={!error && message ? handleOk : undefined}
        />
      )}

      {showStatusConfirm && (
        <ConfirmationMessageBox
          // FIX: Changed "role" to "group"
          message={`Do you want to ${
            is_active ? "deactivate" : "activate"
          } this group?`}
          onConfirm={handleConfirmToggleStatus}
          onCancel={() => setShowStatusConfirm(false)}
        />
      )}
    </Div>
  );
};

export default AddGroup;
