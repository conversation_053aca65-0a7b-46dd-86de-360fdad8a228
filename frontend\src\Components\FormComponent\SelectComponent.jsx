// import React from "react";
// //  Created By <PERSON><PERSON><PERSON>
// function SelectComponent({ options, value, onChange, name, label, width }) {
//   if (!options || !Array.isArray(options)) {
//     return null;
//   }

//   return (
//     <div className="login-field">
//       <div className="my-2 text-left">
//         <label htmlFor={name}>{label}</label>
//       </div>

//       <select
//   name={name}
//   value={value}
//   onChange={onChange}
//   className="login-field-input bg-white text-black border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary hover:border-primary hover:bg-gray-50"
//   style={{ width }}
// >
//   {options.map((option, optIndex) => (
//     <option
//       key={optIndex}
//       value={option.value}
//       className="bg-white text-black"
//     >
//       {option.label}
//     </option>
//   ))}
// </select>

//     </div>
//   );
// }

// export default SelectComponent;
import React, { useState, useRef, useEffect } from "react";
import { FaCaretDown } from "react-icons/fa6"; // 🔽 Import icon

function SelectComponent({ options = [], value, onChange, name, label, width="100%",disabled=false }) {
  const [open, setOpen] = useState(false);
  const dropdownRef = useRef(null);

  const selected = options.find((opt) => opt.value === value);

  // Handle click outside to close dropdown
  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <div className="relative pt-4" style={{ width }} ref={dropdownRef}>
      {label && <label className="block mb-2 text-base ">{label}</label>}

      <div
        onClick={() => setOpen((prev) => !prev)}
        className={`border border-gray-300 rounded px-3 py-2 flex justify-between items-center ${disabled ? 'bg-disabledInput cursor-not-allowed text-black100' : 'bg-white  cursor-pointer'}`}
      >
        <span>{selected?.label || "Select an option"}</span>
        <FaCaretDown  className="ml-2 text-gray-500" /> {/* 🔽 React icon here */}
      </div>

      {open && (
        <ul className="absolute z-20 bg-white border border-gray-300 rounded mt-1 shadow w-full max-h-30 overflow-y-auto">
          {options.map((option, index) => (
            <li
              key={index}
              className={`px-3 py-2 hover:bg-primary hover:text-white cursor-pointer ${
                option.value === value ? "bg-primary text-white" : ""
              }`}
              onClick={() => {
                onChange({ target: { name, value: option.value } });
                setOpen(false);
              }}
            >
              {option.label}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}

export default SelectComponent;
