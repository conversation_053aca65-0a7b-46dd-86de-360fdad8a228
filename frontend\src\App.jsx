import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { setAuthFromLocalStorage } from "./redux/slices/authSlice/authSlice";
import { RouterProvider } from "react-router-dom";
import { router } from "./Routes/Routes.jsx";

const App = () => {
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(setAuthFromLocalStorage()); // Load authentication state from localStorage
  }, [dispatch]);

  return <RouterProvider router={router} />;
};

export default App;
