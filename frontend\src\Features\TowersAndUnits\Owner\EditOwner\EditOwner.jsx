import React, { useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { BiArrowBack } from "react-icons/bi";
import UnitTowerInfo from "../../UnitDetails/components/UnitTowerInfo";
import { useDispatch } from "react-redux";
import EditOwnerForm from "./EditOwnerForm";
import { clearMessage, clearOwnerDetails } from "../../../../redux/slices/owner/ownerSlice";

const EditOwner = () => {
  const { unitId, ownerId } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  useEffect(() => {
    // Clear messages when component unmounts
    return () => {
      dispatch(clearMessage());
      dispatch(clearOwnerDetails());
    };
  }, [dispatch]);

  return (
    <div className="h-full p-[14px]">
      <div className="container mx-auto">
        <div className="flex justify-between max-w-[1282px] py-4">
          <p className="flex items-center text-2xl font-medium">
            <BiArrowBack
              className="mr-2 text-gray-600 cursor-pointer"
              onClick={() => navigate(`/unit-details/${unitId}?tab=2`)}
            />
            Edit Owner
          </p>
        </div>
        <div className="flex flex-col md:flex-row rounded-xl max-w-[1282px] bg-white">
          <UnitTowerInfo id={unitId} />
          <EditOwnerForm unitId={unitId} ownerId={ownerId} />
        </div>
      </div>
    </div>
  );
};

export default EditOwner; 