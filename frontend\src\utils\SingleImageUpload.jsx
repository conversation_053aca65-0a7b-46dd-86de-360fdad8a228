import React, { useState, useEffect } from "react";
import axios from "axios";

const BASE_URL = "http://127.0.0.1:8000"; // Set your actual backend URL

const SingleImageUpload = ({ file, altImg, customClass }) => {
  const [previewMedia, setPreviewMedia] = useState(null);

  useEffect(() => {
    if (file) {
      if (typeof file === "string") {
        // If file is a URL, ensure it's a complete path
        const imageUrl = file.startsWith("http") ? file : `${BASE_URL}${file}`;
        setPreviewMedia({ src: imageUrl, id: file });
      } else if (file instanceof File) {
        // If file is a File object, preview it
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onloadend = () => {
          setPreviewMedia({ src: reader.result, id: file.name + file.lastModified });
        };
      }
    } else {
      // Show the default image
      setPreviewMedia({ src: altImg, id: "default" });
    }
  }, [file, altImg]);

  return (
    <div>
      {previewMedia && (
        <img
          src={previewMedia.src}
          alt="Uploaded Image Preview"
          className={`rounded-lg shadow-lg w-full h-full object-contain ${customClass}`}
        />
      )}
    </div>
  );
};

export default SingleImageUpload;