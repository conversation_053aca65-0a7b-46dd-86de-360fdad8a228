import { useRef, useEffect } from "react";
import { FaEdit, FaTrash, FaThumbtack, FaHistory, FaClock, FaBell } from "react-icons/fa";
import { MdRestore } from "react-icons/md";

const NoticeActionMenu = ({
  notice,
  onEdit,
  onDelete,
  onExpire,
  onRestore,
  onViewHistory,
  onPinToggle,
  onReminder,
  onClose,
  activeTab
}) => {
  const menuRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        onClose();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [onClose]);

  const menuItems = [];

  // Reminder action (available for ongoing and upcoming)
  if (activeTab === 1 || activeTab === 2) {
    menuItems.push({
      icon: FaBell,
      label: "Reminder",
      action: () => onReminder && onReminder(notice),
      className: "text-gray-600 hover:bg-gray-50"
    });
  }

  // Edit action (available for all tabs)
  menuItems.push({
    icon: FaEdit,
    label: "Edit",
    action: () => onEdit && onEdit(notice.id),
    className: "text-blue-600 hover:bg-blue-50"
  });

  // History action (available for all tabs)
  menuItems.push({
    icon: FaHistory,
    label: "History",
    action: () => onViewHistory && onViewHistory(notice),
    className: "text-gray-600 hover:bg-gray-50"
  });

  // Move to Expired action (available for ongoing and upcoming)
  if (activeTab === 1 || activeTab === 2) {
    menuItems.push({
      icon: FaClock,
      label: "Move Expired",
      action: () => onExpire && onExpire(notice),
      className: "text-orange-600 hover:bg-orange-50"
    });
  }

  // Pin/Unpin action (available for ongoing and upcoming)
  if (activeTab === 1 || activeTab === 2) {
    menuItems.push({
      icon: FaThumbtack,
      label: notice.is_pinned ? "Unpin Post" : "Pin Post",
      action: () => onPinToggle && onPinToggle(notice),
      className: notice.is_pinned
        ? "text-red-600 hover:bg-red-50"
        : "text-green-600 hover:bg-green-50"
    });
  }

  // Restore action (available for expired)
  if (activeTab === 3) {
    menuItems.push({
      icon: MdRestore,
      label: "Restore",
      action: () => onRestore && onRestore(notice),
      className: "text-green-600 hover:bg-green-50"
    });
  }

  return (
    <div
      ref={menuRef}
      className="absolute right-0 top-8 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50"
    >
      {menuItems.map((item, index) => (
        <button
          key={index}
          onClick={item.action}
          className={`w-full px-4 py-2 text-left flex items-center gap-3 transition-colors ${item.className}`}
        >
          <item.icon className="w-4 h-4" />
          <span className="text-sm font-medium">{item.label}</span>
        </button>
      ))}
    </div>
  );
};

export default NoticeActionMenu;
