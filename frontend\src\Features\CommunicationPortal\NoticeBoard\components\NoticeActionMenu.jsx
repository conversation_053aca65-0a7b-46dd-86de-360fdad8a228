import { useRef, useEffect } from "react";
import { FaEdit, FaTrash, FaThumbtack, FaHistory, FaClock } from "react-icons/fa";
import { MdRestore } from "react-icons/md";

const NoticeActionMenu = ({ notice, onAction, onClose, activeTab }) => {
  const menuRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        onClose();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [onClose]);

  const handleAction = (action) => {
    onAction(action, notice);
  };

  const menuItems = [];

  // Edit action (available for all tabs)
  menuItems.push({
    icon: FaEdit,
    label: "Edit",
    action: "edit",
    className: "text-blue-600 hover:bg-blue-50"
  });

  // Pin/Unpin action (available for ongoing and upcoming)
  if (activeTab === 1 || activeTab === 2) {
    menuItems.push({
      icon: FaThumbtack,
      label: notice.isPinned ? "Unpin" : "Pin",
      action: notice.isPinned ? "unpin" : "pin",
      className: notice.isPinned 
        ? "text-red-600 hover:bg-red-50" 
        : "text-green-600 hover:bg-green-50"
    });
  }

  // Expire action (available for ongoing and upcoming)
  if (activeTab === 1 || activeTab === 2) {
    menuItems.push({
      icon: FaClock,
      label: "Move to Expired",
      action: "expire",
      className: "text-orange-600 hover:bg-orange-50"
    });
  }

  // Restore action (available for expired)
  if (activeTab === 3) {
    menuItems.push({
      icon: MdRestore,
      label: "Restore",
      action: "restore",
      className: "text-green-600 hover:bg-green-50"
    });
  }

  // History action (available for all tabs)
  menuItems.push({
    icon: FaHistory,
    label: "View History",
    action: "history",
    className: "text-gray-600 hover:bg-gray-50"
  });

  // Delete action (available for all tabs)
  menuItems.push({
    icon: FaTrash,
    label: "Delete",
    action: "delete",
    className: "text-red-600 hover:bg-red-50"
  });

  return (
    <div
      ref={menuRef}
      className="absolute right-0 top-8 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50"
    >
      {menuItems.map((item, index) => (
        <button
          key={index}
          onClick={() => handleAction(item.action)}
          className={`w-full px-4 py-2 text-left flex items-center gap-3 transition-colors ${item.className}`}
        >
          <item.icon className="w-4 h-4" />
          <span className="text-sm font-medium">{item.label}</span>
        </button>
      ))}
    </div>
  );
};

export default NoticeActionMenu;
