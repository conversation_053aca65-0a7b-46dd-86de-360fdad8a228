import React, { useEffect, useState, useMemo, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { fetchCommMembers } from "../../../redux/slices/commMember/commMemberSlice";
import FilterSelect3 from "Components/FilterSelect/FilterSelect3";
import SearchBar from "Components/Search/SearchBar";
import CommunityMemberListTable from "Components/Table/Member/CommunityMemberListTable";
import TableSkeleton from "Components/Loaders/TableSkeleton";
import Heading from "Components/HeadingComponent/Heading";
import { Div } from "Components/Ui/Div";
import Button from "../../../Components/FormComponent/ButtonComponent/Button";
import { RiFilter3Fill } from "react-icons/ri";
import { FaPrint, FaDownload } from "react-icons/fa";
import { exportToExcel, printTable } from "utils/exportPrintExcel";
import logo from "./../../../assets/user/user.png";
import FilterButton from "../../../Components/FormComponent/ButtonComponent/FilterButton";
import { checkPermission } from "../../../utils/permissionUtils";
import { useNavigate } from "react-router-dom";

const CommunityMemberList = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  // Permission state
  const [hasPermission, setHasPermission] = useState(false);
  const [loadingPermission, setLoadingPermission] = useState(true);

  // All other hooks must be called before any early return
  const { items: members, loading, error } = useSelector((s) => s.commMember);
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearch, setDebouncedSearch] = useState("");
  const [allMembers, setAllMembers] = useState([]);
  const [filters, setFilters] = useState({
    type: [],
    tower: [],
    unit: [],
    status: []
  });
  const [showFilters, setShowFilters] = useState(false);
  const [filterActive, setFilterActive] = useState(false);

  useEffect(() => {
    const fetchPermission = async () => {
      const permissionGranted = await checkPermission("org", 24);
      setHasPermission(permissionGranted);
      setLoadingPermission(false);
    };
    fetchPermission();
  }, []);

 
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearch(searchTerm.trim());
    }, 500);
    return () => clearTimeout(handler);
  }, [searchTerm]);

  // seed allMembers once when the fetch returns
  useEffect(() => {
    if (members.length > 0 && allMembers.length === 0) {
      setAllMembers(members);
    }
  }, [members, allMembers]);

  // fetch whenever any filter array OR the debounced search changes
  useEffect(() => {
    dispatch(
      fetchCommMembers({
        ...filters,
        // only send search if ≥ 3 chars
        ...(debouncedSearch.length >= 3 ? { search: debouncedSearch } : {})
      })
    );
  }, [
    dispatch,
    filters.type,
    filters.tower,
    filters.unit,
    filters.status,
    debouncedSearch
  ]);

  // options (memoized)
  const memberTypeOptions = useMemo(
    () => [
      { value: "owner", label: "Owner" },
      { value: "resident", label: "Resident" },
      { value: "staff", label: "Staff" }
    ],
    []
  );

  // const towerOptions = useMemo(
  //   () =>
  //     Array.from(new Set(allMembers.map((m) => m.tower))).map((v) => ({
  //       value: v,
  //       label: v
  //     })),
  //   [allMembers]
  // );

   const towerOptions = useMemo(
    () =>
      Array.from(new Set(members.map((m) => m.tower)))
        .filter(Boolean) // Remove empty values
        .map((v) => ({
          value: v,
          label: v
        })),
    [members] // Update when members change
  );

  const unitOptions = useMemo(
    () =>
      Array.from(new Set(allMembers.map((m) => m.unit))).map((v) => ({
        value: v,
        label: v
      })),
    [allMembers]
  );

  const statusOptions = useMemo(
    () => [
      { value: "Active", label: "Active" },
      { value: "Inactive", label: "Inactive" }
    ],
    []
  );

  const handlePrint = useCallback(() => {
    const title = "Members List";
    const fileName = `EstateLink Community Member List ${
      new Date().toISOString().split("T")[0]
    }.pdf`;

    // Group members by ID and combine their properties
    const memberGroups = members.reduce((groups, member) => {
      if (!groups[member.id]) {
        groups[member.id] = {
          ...member,
          types: new Set(),
          towers: new Set(),
          units: new Set()
        };
      }

      // Add current member's properties to the sets
      groups[member.id].types.add(member.type);
      groups[member.id].towers.add(member.tower);
      groups[member.id].units.add(member.unit);

      return groups;
    }, {});

    // Prepare data for printing
    const printData = Object.values(memberGroups).map((member) => ({
      full_name: member.full_name || "N/A",
      general_email: member.general_email || "N/A",
      general_contact: member.general_contact || "N/A",
      type: Array.from(member.types)
        .map((t) => t.charAt(0).toUpperCase() + t.slice(1).toLowerCase())
        .join(", "),
      tower: Array.from(member.towers).join(", "),
      unit: Array.from(member.units).join(", "),
      status:
        member.status ||
        (Number(member.is_org_member) === 1 ? "Active" : "Inactive")
    }));

    const columns = [
      { header: "Name", accessor: (m) => m.full_name },
      { header: "Email", accessor: (m) => m.general_email },
      { header: "Contact", accessor: (m) => m.general_contact },
      { header: "Type", accessor: (m) => m.type },
      { header: "Tower", accessor: (m) => m.tower },
      { header: "Unit", accessor: (m) => m.unit },
      { header: "Status", accessor: (m) => m.status }
    ];

    printTable(printData, columns, title, logo, fileName);
  }, [members, logo]);

  const exportData = useMemo(() => {
    // Group members by ID and combine their properties
    const memberGroups = members.reduce((groups, member) => {
      if (!groups[member.id]) {
        groups[member.id] = {
          ...member,
          types: new Set(),
          towers: new Set(),
          units: new Set()
        };
      }

      // Add current member's properties to the sets
      groups[member.id].types.add(member.type);
      groups[member.id].towers.add(member.tower);
      groups[member.id].units.add(member.unit);

      return groups;
    }, {});

    // Prepare data for Excel export
    return Object.values(memberGroups).map((member) => ({
      Name: member.full_name || "N/A",
      Email: member.general_email || "N/A",
      Contact: member.general_contact || "N/A",
      Type: Array.from(member.types)
        .map((t) => t.charAt(0).toUpperCase() + t.slice(1).toLowerCase())
        .join(", "),
      Tower: Array.from(member.towers).join(", "),
      Unit: Array.from(member.units).join(", "),
      Status:
        member.status ||
        (Number(member.is_org_member) === 1 ? "Active" : "Inactive")
    }));
  }, [members]);
  const handleExport = useCallback(() => {
    exportToExcel(
      exportData,
      "EstateLink Community Member List",
      "Community Member List"
    );
  }, [exportData]);

  const toggleFilters = () => {
    setShowFilters((prev) => !prev);
    setFilterActive((prev) => !prev);
  };

  if (loadingPermission) return <div className="p-4">Loading...</div>;
  if (!hasPermission) {
    navigate("/not-authorized");
    return null;
  }

  return (
    <Div className="p-2">
      <Div className="container">
        <Div className="relative shadow rounded-27 bg-white py-4 px-4">
          <Div className="flex justify-between items-center py-4 mb-4">
            <Heading
              title=" Members List"
              size="xl"
              color="text-black"
            />

            <Div className="flex items-center space-x-4">
              <Button
                icon={FaDownload}
                variant="download"
                size="large"
                onClick={handleExport}
                disabled={!members.length}
                iconSize="medium"
              ></Button>
              <Button
                variant="download"
                size="large"
                icon={FaPrint}
                onClick={handlePrint}
                disabled={!members.length}
                iconSize="medium"
              ></Button>
              {/* <Button
                variant="download"
                size="xl"
                iconSize="xl"
                icon={RiFilter3Fill}
                onClick={toggleFilters}
              >
                Filter
                
              </Button> */}

              <FilterButton active={filterActive} onClick={toggleFilters}>
                Filter
              </FilterButton>
            </Div>
          </Div>
          {showFilters && (
            <Div className="flex justify-end items-center space-x-4 mt-4 pb-4">
              <FilterSelect3
                placeholder="Member Type"
                options={memberTypeOptions}
                value={filters.type}
                onApply={(val) => setFilters((f) => ({ ...f, type: val }))}
              />
              <FilterSelect3
                placeholder="Tower"
                options={towerOptions}
                value={filters.tower}
                onApply={(val) => setFilters((f) => ({ ...f, tower: val }))}
              />
              <FilterSelect3
                placeholder="Unit"
                options={unitOptions}
                value={filters.unit}
                onApply={(val) => setFilters((f) => ({ ...f, unit: val }))}
              />
              <FilterSelect3
                placeholder="Status"
                options={statusOptions}
                value={filters.status}
                onApply={(val) => setFilters((f) => ({ ...f, status: val }))}
              />
              {/* SearchBar now only updates local searchTerm */}
              <SearchBar
                placeholder="Search name or email…"
                updateUrl={false}
                onSearch={(text) => setSearchTerm(text)}
              />
            </Div>
          )}

          {loading ? (
            <div className="flex items-center justify-center my-12">
              <TableSkeleton />
            </div>
          ) : members.length === 0 ? (
            <div className="text-center my-12 text-gray-500">
              No results found
            </div>
          ) : (
            <CommunityMemberListTable members={members} error={error} />
          )}
        </Div>
      </Div>
    </Div>
  );
};

export default CommunityMemberList;
