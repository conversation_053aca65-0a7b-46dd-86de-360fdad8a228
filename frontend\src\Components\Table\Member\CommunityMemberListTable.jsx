import React from "react";
import { FaEye } from "react-icons/fa";
import { Link, useLocation } from "react-router-dom";
import user1 from "../../../assets/user/user.png";
import { setActiveTabs } from "../../../redux/slices/memberSlice";
import { useDispatch } from "react-redux";
import NoData from "../NoData";

const baseURL = import.meta.env.VITE_BASE_API;

const CommunityMemberListTable = ({ members, error }) => {
  const dispatch = useDispatch();
  const location = useLocation();
  const hasMembers = Array.isArray(members) && members.length > 0;

  // Group members by ID and collect their locations
  const groupedMembers = members.reduce((acc, member) => {
    if (!acc[member.id]) {
      acc[member.id] = {
        ...member,
        locations: []
      };
    }
    acc[member.id].locations.push({
      type: member.type,
      tower: member.tower,
      unit: member.unit
    });
    return acc;
  }, {});

  return (
    <div className="w-full">
      <div className="relative overflow-x-auto max-h-[70vh] overflow-y-auto bg-white">
        <table className="w-full text-sm text-left rtl:text-right">
          <thead className="bg-subprimary border-b border-subprimary sticky top-0 z-10">
            <tr>
              <th className="px-3 font-[700] py-2 text-base text-left">Name</th>
              <th className="px-3 font-[700] py-2 text-base text-left">
                Contact
              </th>
              <th className="px-3 font-[700] py-2 text-base text-left">
                Email
              </th>
              <th className="px-3 font-[700] py-2 text-base text-left">Type</th>
              {/* <th className="px-3 font-[700] py-2 text-base text-left">
                Occupation
              </th> */}
              <th className="px-3 font-[700] py-2 text-base text-left">
                Tower
              </th>
              <th className="px-3 font-[700] py-2 text-base text-left">Unit</th>
              <th className="px-3 font-[700] py-2 text-base text-center">
                Status
              </th>
              <th className="px-3 font-[700] py-2 text-base text-center">
                Action
              </th>
            </tr>
          </thead>
          <tbody>
            {!hasMembers ? (
              <tr>
                <td colSpan="9" className="py-4">
                  <NoData />
                </td>
              </tr>
            ) : (
              Object.values(groupedMembers).map((member) => (
                <React.Fragment key={member.id}>
                  {/* First row with member info */}
                  <tr className="bg-white hover:bg-gray-50">
                    <td className="px-3 py-3 text-base text-left border-b border-gray-200">
                      <div className="flex items-center">
                        <img
                          src={
                            member.photo ? `${baseURL}${member.photo}` : user1
                          }
                          alt="User"
                          className="w-6 h-6 rounded-full mr-2"
                        />
                        {member.full_name}
                      </div>
                    </td>
                    <td className="px-3 py-3 text-base text-left border-b border-gray-200">
                      {member.general_contact}
                    </td>
                    <td className="px-3 py-3 text-base text-left border-b border-gray-200">
                      {member.general_email}
                    </td>
                    <td className="px-3 py-3 text-base text-left border-b border-gray-200">
                      {member.locations[0].type
                        ? member.locations[0].type.charAt(0).toUpperCase() +
                          member.locations[0].type.slice(1).toLowerCase()
                        : "---"}
                    </td>

                    {/* <td className="px-3 py-3 text-base text-left border-b border-gray-200">
                      {member.occupation}
                    </td> */}
                    <td className="px-3 py-3 text-base text-left border-b border-gray-200">
                      {member.locations[0].tower}
                    </td>
                    <td className="px-3 py-3 text-base text-left border-b border-gray-200">
                      {member.locations[0].unit}
                    </td>
                    <td className="px-3 py-3 text-base text-center border-b border-gray-200">
                      <span
                        className={`mx-2 py-2 px-2 rounded-8 text-white  ${
                          member.status === "Active"
                            ? "bg-primary"
                            : "bg-secondary"
                        }`}
                      >
                        {member.status}
                      </span>
                    </td>
                    {/* <td className="px-3 py-3 text-base  text-center border-b border-gray-200">
                      <div className="text-centerflex justify-center">
                        <Link
                        to={{
                          pathname: `/member-profile/${member.id}`,
                          state: { from: location.pathname }
                        }}
                        onClick={() => dispatch(setActiveTabs(1))}
                      >
                        <FaEye  className="w-[25px] h-[20px] text-center text-primary" />
                      </Link>
                      </div>
                    </td> */}
                    <td className="px-3 py-3 text-base text-center border-b border-gray-200">
                      <div className="flex justify-center">
                        <Link
                          to={{
                            pathname: `/member-profile/${member.id}`,
                            state: { from: location.pathname }
                          }}
                          onClick={() => dispatch(setActiveTabs(1))}
                        >
                          <FaEye className="w-[25px] h-[20px] text-primary" />
                        </Link>
                      </div>
                    </td>
                  </tr>
                  {/* Additional rows for other locations */}
                  {member.locations.slice(1).map((location, index) => (
                    <tr
                      key={`${member.id}-${index + 1}`}
                      className="bg-white hover:bg-gray-50"
                    >
                      <td className="px-3 py-3 text-base text-left"></td>
                      <td className="px-3 py-3 text-base text-left"></td>
                      <td className="px-3 py-3 text-base text-left"></td>
                      <td className="px-3 py-3 text-base text-left border-b border-gray-200">
                        {/* {location.type} */}
                         {location.type
                        ? location.type.charAt(0).toUpperCase() +
                          location.type.slice(1).toLowerCase()
                        : "---"}
                      </td>
                      {/* <td className="px-3 py-3 text-base text-left border-b border-gray-200">
                        {member.occupation}
                      </td> */}
                      <td className="px-3 py-3 text-base text-left border-b border-gray-200">
                        {location.tower}
                      </td>
                      <td className="px-3 py-3 text-base text-left border-b border-gray-200">
                        {location.unit}
                      </td>
                      <td className="px-3 py-3 text-base text-left"></td>
                      <td className="px-3 py-3 text-base text-left"></td>
                    </tr>
                  ))}
                </React.Fragment>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default CommunityMemberListTable;
