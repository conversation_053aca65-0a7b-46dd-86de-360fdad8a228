import React, { useState, useEffect } from "react";
import { BiArrowBack } from "react-icons/bi";
import { Link, useParams, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import TextInputComponent from "../../../Components/FormComponent/TextInputComponent";
import TextareaComponent from "../../../Components/FormComponent/TextareaComponent";
import {
  fetchPermissions,
  createRole,
  updateRole,
  clearMessages,
  fetchRoleDetails,
  toggleRoleStatus
} from "../../../redux/slices/roles/rolesSlice";
import SubmitButton from "../../../Components/FormComponent/ButtonComponent/SubmitButton";
import MessageBox from "../../../Components/MessageBox/MessageBox";
import ConfirmationMessageBox from "../../../Components/MessageBox/ConfirmationMessageBox";
import ErrorMessage from "../../../Components/MessageBox/ErrorMessage";
import { checkPermission } from "../../../utils/permissionUtils";
import CheckboxComponent from "../../../Components/FormComponent/CheckboxComponent";
import { Div } from "Components/Ui/Div";

const AddRole = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const {
    permissions,
    loading,
    error: backendError,
    successMessage,
    roleDetails
  } = useSelector((state) => state.role);
  const editMode = Boolean(id);

  const [formData, setFormData] = useState({
    role_name: "",
    role_description: "",
    permissions: []
  });
  const [errors, setErrors] = useState({ role_name: "" });
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showStatusConfirm, setShowStatusConfirm] = useState(false); // ✅ NEW
  const [hasPermission, setHasPermission] = useState(false);
  const [loadingPermission, setLoadingPermission] = useState(true);
  const [isFormChanged, setIsFormChanged] = useState(false);

  useEffect(() => {
    dispatch(clearMessages());
  }, [dispatch]);

  useEffect(() => {
    const fetchPermission = async () => {
      const permissionId = editMode ? 5 : 4;
      const permissionGranted = await checkPermission("org", permissionId);
      setHasPermission(permissionGranted);
      setLoadingPermission(false);
    };
    fetchPermission();
    dispatch(fetchPermissions());
    if (editMode) dispatch(fetchRoleDetails(id));
  }, [dispatch, editMode, id]);

  useEffect(() => {
    if (editMode && roleDetails) {
      setFormData({
        role_name: roleDetails.role_name || "",
        role_description: roleDetails.role_description || "",
        permissions: roleDetails.selected_permissions || []
      });
    }
  }, [editMode, roleDetails]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    setErrors((prev) => ({ ...prev, [name]: "" }));

    if (name === "role_description" && value.length > 255) {
      setErrors((prev) => ({
        ...prev,
        role_description: "Role description cannot exceed 255 characters"
      }));
    }
  };

  const handleCheckboxChange = (e, permissionId) => {
    setFormData((prev) => ({
      ...prev,
      permissions: e.target.checked
        ? [...prev.permissions, permissionId]
        : prev.permissions.filter((id) => id !== permissionId)
    }));
  };

  const handleSelectAllChange = (e) => {
    setFormData((prev) => ({
      ...prev,
      permissions: e.target.checked ? permissions.map((p) => p.id) : []
    }));
  };

  const handleUpdateConfirmed = () => {
    setShowConfirmation(false);
    dispatch(updateRole({ id, roleData: formData }))
      .unwrap()
      .catch((err) => {
        setErrors({
          role_name: err.role_name ? err.role_name[0] : "",
          role_description: err.role_description ? err.role_description[0] : ""
        });
      });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    setErrors({ role_name: "", role_description: "" });

    const validationErrors = {};
    if (!formData.role_name.trim()) {
      validationErrors.role_name = "Role name is required";
    }
    if (formData.role_description.length > 255) {
      validationErrors.role_description =
        "Role description cannot exceed 255 characters";
    }

    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    if (editMode) {
      const noChanges =
        formData.role_name === roleDetails?.role_name &&
        formData.role_description === roleDetails?.role_description &&
        JSON.stringify([...formData.permissions].sort()) ===
          JSON.stringify([...(roleDetails?.selected_permissions || [])].sort());
      if (noChanges) return;
      setShowConfirmation(true);
    } else {
      dispatch(createRole(formData))
        .unwrap()
        .catch((err) => {
          if (err.role_name || err.role_description) {
            console.log("Backend Error:", err);
          }
        });
    }
  };

  const handleClearMessage = () => {
    dispatch(clearMessages());
    navigate(editMode ? `/roleProfile/${id}` : "/role-list");
  };

  const checkFormChanged = () => {
    if (!editMode) {
      return (
        formData.role_name.trim() !== "" ||
        formData.role_description.trim() !== "" ||
        formData.permissions.length > 0
      );
    } else {
      return (
        formData.role_name !== roleDetails?.role_name ||
        formData.role_description !== roleDetails?.role_description ||
        JSON.stringify([...formData.permissions].sort()) !==
          JSON.stringify([...(roleDetails?.selected_permissions || [])].sort())
      );
    }
  };

  useEffect(() => {
    setIsFormChanged(checkFormChanged());
  }, [formData, roleDetails, editMode]);

  // ✅ Show confirm modal for toggle
  const handleToggleStatus = () => {
    setShowStatusConfirm(true);
  };

  // ✅ Actual toggle only after confirm
  const handleConfirmToggleStatus = () => {
    setShowStatusConfirm(false);
    dispatch(toggleRoleStatus(id));
  };

  if (loadingPermission) return null;
  if (!hasPermission) navigate("/not-authorized");

  // const { is_active } = roleDetails;
  const is_active = editMode && roleDetails ? roleDetails.is_active : false;

  const shouldScroll = permissions.length > 6;

 
  const MEMBER_PERMS = [
    "Create Member", "Edit Member", "View Member List",
    "Create Role", "Edit Role", "View Role List",
    "Create Group", "Edit Group", "View Group List"
  ];
  const TOWER_PERMS = [
    "Create Tower", "Edit Tower", "View Tower",
    "View Unit Details","Edit Unit Details",
    "View Unit Ownership","Add Ownership","Change Ownership",
    "View Unit Resident","Add Resident","Edit ResidentInfo",
    "View Unit Staff","Add Unit Staff","Edit Unit Staff","View Community Member List"
  ];

  return (
    <div className="h-full p-[14px]">
      <div className="container">
        <form onSubmit={handleSubmit}>
          <div className="mb-2">
            <div className="flex justify-between items-center">
              <Link to={editMode ? `/roleProfile/${id}` : "/role-list"}>
                <p className="flex items-center font-medium text-[24px]">
                  <BiArrowBack className="mr-2 text-gray-600" />
                  {editMode ? "Edit Role" : "Add Role"}
                </p>
              </Link>
              <div className="flex mx-2">
                {editMode && (
                  <button
                    type="button"
                    onClick={handleToggleStatus}
                    className={`px-4 py-2 my-3  mr-2 font-semibold  rounded transition-all duration-200 ${
                      is_active
                        ? "border-primary bg-primary text-white"
                        : "border-error bg-error text-white"
                    }`}
                  >
                    <span className="px-1 text-base">
                      {is_active ? "Active" : "Inactive"}
                    </span>
                  </button>
                )}

                <SubmitButton
                  text={editMode ? "Update" : "Create"}
                  loading={loading}
                  disabled={!isFormChanged || loading}
                  onClick={handleSubmit}
                  bgColor={
                    !isFormChanged || loading
                      ? "bg-white cursor-not-allowed"
                      : "bg-primary"
                  }
                  textColor={
                    !isFormChanged || loading ? "text-primary" : "text-white"
                  }
                />
              </div>
            </div>
          </div>

          <div className="bg-white border p-[14px] rounded-[27px] mx-auto">
            <TextInputComponent
              name="role_name"
              label="Role Name"
              placeholder="Write Role Name"
              value={formData.role_name}
              onChange={handleChange}
            />
            {errors.role_name && <ErrorMessage message={errors.role_name} />}

            <div className="mt-4">
              <p className="mb-2 text-sm font-medium">Role Description</p>
              <TextareaComponent
                name="role_description"
                value={formData.role_description}
                onChange={handleChange}
                placeholder="Write Role Description"
                rows={5}
              />
              {errors.role_description && (
                <ErrorMessage message={errors.role_description} />
              )}
            </div>

            <div className="mt-6">
              <h2 className="text-[20px] font-semibold mb-4">Role Permissions</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Render permission groups with a helper function to avoid repetition */}
                {(() => {
                  const groups = [
                    {
                      title: "Member Management",
                      permNames: MEMBER_PERMS,
                      selectAllName: "select_all_left"
                    },
                    {
                      title: "Tower and  Unit Management",
                      permNames: TOWER_PERMS,
                      selectAllName: "select_all_right"
                    }
                  ];
                  return groups.map(group => {
                    const groupPerms = permissions.filter(p => group.permNames.includes(p.permission_name));
                    const groupChecked = groupPerms.length > 0 &&
                      formData.permissions.filter(pid => groupPerms.find(p => p.id === pid)).length === groupPerms.length;
                    return (
                      <div key={group.title} className="bg-white rounded-[10px] p-2 border">
                        <h3 className="text-base font-semibold mb-2 text-primary">{group.title}</h3>
                        <Div className="mb-2">
                          <CheckboxComponent
                            name={group.selectAllName}
                            label="Select All"
                            checked={groupChecked}
                            onChange={e => {
                              const groupIds = groupPerms.map(p => p.id);
                              setFormData(prev => ({
                                ...prev,
                                permissions: e.target.checked
                                  ? Array.from(new Set([...prev.permissions, ...groupIds]))
                                  : prev.permissions.filter(pid => !groupIds.includes(pid))
                              }));
                            }}
                            value={group.selectAllName}
                          />
                        </Div>
                        <div className={shouldScroll ? "max-h-60 overflow-y-auto pr-2" : ""}>
                          {groupPerms.map(perm => (
                            <Div key={perm.id} className="mb-2">
                              <CheckboxComponent
                                name="permissions"
                                label={perm.permission_name}
                                checked={formData.permissions.includes(perm.id)}
                                onChange={e => handleCheckboxChange(e, perm.id)}
                                value={perm.id}
                              />
                            </Div>
                          ))}
                        </div>
                      </div>
                    );
                  });
                })()}
              </div>
            </div>
          </div>
        </form>

        {backendError && (
          <MessageBox
            type="error"
            error
            message={
              backendError.role_name?.[0] ||
              backendError.detail ||
              "An error occurred."
            }
            clearMessage={() => dispatch(clearMessages())}
            onOk={() => dispatch(clearMessages())}
          />
        )}

        {successMessage && (
          <MessageBox
            message={successMessage}
            clearMessage={handleClearMessage}
            onOk={handleClearMessage}
          />
        )}

        {showConfirmation && (
          <ConfirmationMessageBox
            message="Are you sure you want to update this role?"
            onConfirm={handleUpdateConfirmed}
            onCancel={() => setShowConfirmation(false)}
          />
        )}

        {/* ✅ Status confirmation modal */}
        {showStatusConfirm && (
          <ConfirmationMessageBox
            message={`Do you want to ${
              is_active ? "deactivate" : "activate"
            } this role?`}
            onConfirm={handleConfirmToggleStatus}
            onCancel={() => setShowStatusConfirm(false)}
          />
        )}
      </div>
    </div>
  );
};

export default AddRole;
