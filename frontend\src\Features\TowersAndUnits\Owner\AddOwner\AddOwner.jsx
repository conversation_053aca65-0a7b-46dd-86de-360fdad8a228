import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { BiArrowBack } from "react-icons/bi";
import UnitTowerInfo from "../../UnitDetails/components/UnitTowerInfo";
import { useDispatch } from "react-redux";
import AddOwnerForm from "./AddOwnerForm";
import AddCompany from "../AddCompany/AddCompany";
import { clearMessage } from "../../../../redux/slices/owner/ownerSlice";
import { checkPermission } from "../../../../utils/permissionUtils";

const AddOwner = () => {
  const { unitId } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  // Permission state
  const [hasPermission, setHasPermission] = useState(false);
  const [loadingPermission, setLoadingPermission] = useState(true);

  useEffect(() => {
    const fetchPermission = async () => {
      // Use the correct permission type and id for add owner (comm, 85)
      const permissionGranted = await checkPermission("org", 16);
      setHasPermission(permissionGranted);
      setLoadingPermission(false);
    };
    fetchPermission();
  }, []);

  useEffect(() => {
    // Clear messages when component unmounts
    return () => {
      dispatch(clearMessage());
    };
  }, [dispatch]);

  if (loadingPermission) return <div className="p-4">Loading...</div>;
  if (!hasPermission) {
    navigate("/not-authorized");
    return null;
  }

  return (
    <div className="h-full p-[14px]">
      <div className="container mx-auto">
        <div className="flex justify-between max-w-[1282px] py-4">
          <p className="flex items-center text-2xl font-medium">
            <BiArrowBack
              className="mr-2 text-gray-600 cursor-pointer"
              onClick={() => navigate(`/unit-details/${unitId}?tab=2`)}
            />
            Add Owner
          </p>
        </div>
        <div className="flex flex-col md:flex-row rounded-xl max-w-[1282px] bg-white">
          <UnitTowerInfo id={unitId} />
          <AddOwnerForm unitId={unitId} />
        </div>
      </div>
    </div>
  );
};

export default AddOwner;