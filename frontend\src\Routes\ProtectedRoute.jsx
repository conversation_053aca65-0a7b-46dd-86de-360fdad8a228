// src/routes/ProtectedRoute.jsx
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { Navigate } from "react-router-dom";
import axiosInstance from "../utils/axiosInstance";

const ProtectedRoute = ({ children, requiredPermission }) => {
  const user = useSelector((state) => state.auth.user);
  // Always declare hooks at the top.
  const [centralPermissionGranted, setCentralPermissionGranted] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // If there's no user, no need to check permissions; finish loading.
    if (!user) {
      setLoading(false);
      return;
    }

    const checkCentralPermission = async () => {
      if (!requiredPermission) {
        setCentralPermissionGranted(true);
        setLoading(false);
        return;
      }
      try {
        // Check the central API for permission.
        const response = await axiosInstance.get(
          `/user/cental_permission_checker/?type_of_member=org&permission_id=${requiredPermission}`
        );
        setCentralPermissionGranted(response.status === 200);
      } catch (error) {
        setCentralPermissionGranted(false);
      } finally {
        setLoading(false);
      }
    };

    checkCentralPermission();
  }, [user, requiredPermission]);

  // Now handle redirection or rendering.
  // If user is not authenticated, redirect to login.
  if (!user) {
    return <Navigate to="/login" replace />;
  }

  // Show a loading state while checking permissions.
  if (loading) {
    return <div>Loading...</div>;
  }

  // If central permission check fails, redirect to not authorized.
  if (!centralPermissionGranted) {
    return <Navigate to="/not-authorized" replace />;
  }

  // Additionally, check if the user token has the required permission.
  if (requiredPermission) {
    const permissionIds = user.permission_ids.map(String);
    const reqPermission = String(requiredPermission);
    if (!permissionIds.includes(reqPermission)) {
      return <Navigate to="/not-authorized" replace />;
    }
  }

  return children;
};

export default ProtectedRoute;
