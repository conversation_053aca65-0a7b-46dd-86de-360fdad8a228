import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  CreatememberForUnit,
  setCreatedMember,
  clearMessage as clearOwnerMessage
} from "../../../../redux/slices/owner/ownerSlice";

const useMemberSubmit = (formData, validateForm, activeTab, onClose) => {
  const dispatch = useDispatch();
  const { message, error } = useSelector((state) => state.owner);
  const [showMessage, setShowMessage] = useState(false);
  const [fieldErrors, setFieldErrors] = useState({});
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm(formData, activeTab)) return;

    // build FormData
    const fd = new FormData();
    Object.entries(formData).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== "") {
        if (Array.isArray(value)) {
          value.forEach((v) => fd.append(key, v));
        } else {
          fd.append(key, value);
        }
      }
    });

    setLoading(true);
    setFieldErrors({});
    try {
      // unwrap() will either return the fulfilled payload or throw the rejected payload
      const payload = await dispatch(CreatememberForUnit(fd)).unwrap();
      // Success! stash createdMember and show message
      dispatch(
        setCreatedMember({
          id: payload.member.id,
          full_name: payload.member.full_name
        })
      );
      setShowMessage(true);
      // let parent close on success
      if (onClose) onClose();
    } catch (rejectedPayload) {
      // rejectedPayload can be a string or an object of field errors
      if (typeof rejectedPayload === "object") {
        setFieldErrors(rejectedPayload);
      }
      setShowMessage(true);
    } finally {
      setLoading(false);
    }
  };

  // if the global slice error/message changes, we want the MessageBox to open too
  useEffect(() => {
    if (message || error) {
      setShowMessage(true);
    }
  }, [message, error]);

  const handleDismissMessage = () => {
    // clear both global message and local field errors
    dispatch(clearOwnerMessage());
    setFieldErrors({});
    setShowMessage(false);
  };

  return {
    handleSubmit,
    loading,
    showMessage,
    message,
    error,
    fieldErrors,
    handleDismissMessage
  };
};

export default useMemberSubmit;
