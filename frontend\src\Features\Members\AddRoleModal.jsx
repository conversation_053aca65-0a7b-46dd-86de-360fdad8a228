// src/Components/Modals/AddRoleModal.jsx
import React from "react";
import AddMemberRole from "../../Features/Members/OrganizationMemberForm/AddMemberRole";
import { RxCross1 } from "react-icons/rx";

const AddRoleModal = ({ isOpen, onClose, onRoleCreated }) => {
  if (!isOpen) return null;
  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 ">
      {/* Modal backdrop */}
      <div
        className="absolute inset-0 bg-gray-900 opacity-50"
        onClick={onClose}
      ></div>
      {/* Modal content */}
      <div className="relative bg-white rounded-2xl shadow-lg z-10 p-4">
        <button
          className="absolute -top-[8px] -right-[8px] p-2 rounded-full bg-primary text-white shadow-md hover:bg-primary/90 transition z-20"
          onClick={onClose}
        >
          <RxCross1 />
        </button>

        <AddMemberRole onRoleCreated={onRoleCreated} onClose={onClose} />
        {/* <div className="flex justify-end ">
          <button
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded"
            onClick={onClose}
          >
            Cancel
          </button>
        </div> */}
      </div>
    </div>
  );
};

export default AddRoleModal;
