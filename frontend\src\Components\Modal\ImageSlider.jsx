import React from 'react';
import { X, Download, ZoomIn, ZoomOut, ChevronLeft, ChevronRight } from 'lucide-react';
import { useState, useEffect } from 'react';

/**
 * ImageSlider Component
 * Displays multiple images in a modal overlay with navigation, zoom and download functionality
 */
const ImageSlider = ({
    isOpen,
    onClose,
    images = [], // Array of image objects with src, alt, name properties
    initialIndex = 0
}) => {
    const [currentIndex, setCurrentIndex] = useState(initialIndex);
    const [isZoomed, setIsZoomed] = useState(false);

    // Reset zoom when image changes
    useEffect(() => {
        setIsZoomed(false);
    }, [currentIndex]);

    // Reset current index when modal opens
    useEffect(() => {
        if (isOpen) {
            setCurrentIndex(initialIndex);
        }
    }, [isOpen, initialIndex]);

    // Handle keyboard navigation
    useEffect(() => {
        const handleKeyDown = (e) => {
            if (!isOpen) return;

            switch (e.key) {
                case 'Escape':
                    onClose();
                    break;
                case 'ArrowLeft':
                    goToPrevious();
                    break;
                case 'ArrowRight':
                    goToNext();
                    break;
                default:
                    break;
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, [isOpen]);

    // Navigation functions
    const goToNext = () => {
        setCurrentIndex((prev) => (prev + 1) % images.length);
    };

    const goToPrevious = () => {
        setCurrentIndex((prev) => (prev - 1 + images.length) % images.length);
    };

    // Handle backdrop click to close modal
    const handleBackdropClick = (e) => {
        if (e.target === e.currentTarget) {
            onClose();
        }
    };

    // Handle download
    const handleDownload = async () => {
        if (images[currentIndex]) {
            const currentImage = images[currentIndex];
            const imageUrl = currentImage.src || currentImage.url || currentImage.file_url || currentImage;
            const imageName = currentImage.name || currentImage.file_name || `image-${currentIndex + 1}`;

            try {
                const response = await fetch(imageUrl);
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = imageName;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);
            } catch (error) {
                console.error('Download failed:', error);
                // Fallback to simple download
                const link = document.createElement('a');
                link.href = imageUrl;
                link.download = imageName;
                link.target = '_blank';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        }
    };

    // Handle zoom toggle
    const toggleZoom = () => {
        setIsZoomed(!isZoomed);
    };

    // Don't render if modal is not open or no images
    if (!isOpen || !images || images.length === 0) {
        return null;
    }

    const currentImage = images[currentIndex];
    const imageSrc = currentImage?.src || currentImage?.url || currentImage;
    const imageAlt = currentImage?.alt || currentImage?.name || `Image ${currentIndex + 1}`;
    const imageName = currentImage?.name || `image-${currentIndex + 1}`;

    return (
        <div
            className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75 backdrop-blur-sm"
            onClick={handleBackdropClick}
        >
            {/* Modal Container */}
            <div className="relative max-w-4xl max-h-[90vh] w-full mx-4">
                {/* Header */}
                <div className="absolute top-0 left-0 right-0 z-10 flex justify-between items-center p-4 bg-gradient-to-b from-black/50 to-transparent">
                    <div className="text-white">
                        <h3 className="text-lg font-medium truncate">{imageName}</h3>
                        <p className="text-sm text-gray-300">
                            {imageAlt} • {currentIndex + 1} of {images.length}
                        </p>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex items-center space-x-2">
                        <button
                            onClick={toggleZoom}
                            className="p-2 rounded-full bg-primary text-white hover:bg-primary/80 transition-colors"
                            title={isZoomed ? "Zoom Out" : "Zoom In"}
                        >
                            {isZoomed ? (
                                <ZoomOut className="w-5 h-5" />
                            ) : (
                                <ZoomIn className="w-5 h-5" />
                            )}
                        </button>

                        <button
                            onClick={handleDownload}
                            className="p-2 rounded-full bg-primary text-white hover:bg-primary/80 transition-colors"
                            title="Download"
                        >
                            <Download className="w-5 h-5" />
                        </button>

                        <button
                            onClick={onClose}
                            className="p-2 rounded-full bg-primary text-white hover:bg-primary/80 transition-colors"
                            title="Close"
                        >
                            <X className="w-5 h-5" />
                        </button>
                    </div>
                </div>

                {/* Navigation Arrows */}
                {images.length > 1 && (
                    <>
                        {/* Previous Button */}
                        <button
                            onClick={goToPrevious}
                            className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 p-3 rounded-full bg-black/30 text-white hover:bg-black/50 transition-colors"
                            title="Previous Image"
                        >
                            <ChevronLeft className="w-6 h-6" />
                        </button>

                        {/* Next Button */}
                        <button
                            onClick={goToNext}
                            className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 p-3 rounded-full bg-black/30 text-white hover:bg-black/50 transition-colors"
                            title="Next Image"
                        >
                            <ChevronRight className="w-6 h-6" />
                        </button>
                    </>
                )}

                {/* Image Container with Frame */}
                <div className="relative bg-white rounded-lg shadow-2xl overflow-hidden border-4 border-gray-200">
                    {/* Inner Frame */}
                    <div className="p-4 bg-white">
                        <div className="relative border-2 border-gray-300 rounded-md overflow-hidden shadow-inner">
                            <div className={`transition-all duration-300 ${isZoomed ? 'overflow-auto' : 'overflow-hidden'}`}>
                                <img
                                    src={imageSrc}
                                    alt={imageAlt}
                                    className={`w-full h-auto transition-transform duration-300 ${
                                        isZoomed
                                            ? 'transform scale-150 cursor-zoom-out'
                                            : 'cursor-zoom-in max-h-[75vh] object-contain'
                                    }`}
                                    onClick={toggleZoom}
                                    onError={(e) => {
                                        e.target.src = '/placeholder-image.png'; // Fallback image
                                    }}
                                />
                            </div>

                            {/* Frame Corner Decorations */}
                            <div className="absolute top-1 left-1 w-3 h-3 border-l-2 border-t-2 border-gray-400"></div>
                            <div className="absolute top-1 right-1 w-3 h-3 border-r-2 border-t-2 border-gray-400"></div>
                            <div className="absolute bottom-1 left-1 w-3 h-3 border-l-2 border-b-2 border-gray-400"></div>
                            <div className="absolute bottom-1 right-1 w-3 h-3 border-r-2 border-b-2 border-gray-400"></div>
                        </div>
                    </div>
                </div>

                {/* Footer with Image Indicators */}
                <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/50 to-transparent">
                    <div className="text-center">
                        {/* Image Indicators */}
                        {images.length > 1 && (
                            <div className="flex justify-center space-x-2 mb-2">
                                {images.map((_, index) => (
                                    <button
                                        key={index}
                                        onClick={() => setCurrentIndex(index)}
                                        className={`w-2 h-2 rounded-full transition-colors ${
                                            index === currentIndex
                                                ? 'bg-white'
                                                : 'bg-white/50 hover:bg-white/75'
                                        }`}
                                        title={`Go to image ${index + 1}`}
                                    />
                                ))}
                            </div>
                        )}

                        <p className="text-white text-sm">
                            {images.length > 1 ? (
                                <>Use ← → keys or click arrows to navigate • </>
                            ) : null}
                            Click image to zoom • Press ESC to close • Click outside to close
                        </p>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ImageSlider;
