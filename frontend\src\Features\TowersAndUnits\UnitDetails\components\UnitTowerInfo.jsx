import { useEffect } from "react";
import user from "../../../../assets/user/user.png"; // Import the fallback image
import { useDispatch, useSelector } from "react-redux";
import { fetchUnitById } from "../../../../redux/slices/units/unitSlice";
import axios from "axios";

const api = axios.create({
  baseURL: import.meta.env.VITE_BASE_API,
});
const UnitTowerInfo = ({ id }) => {

  const dispatch = useDispatch();
  const { selectedUnit, status, error } = useSelector((state) => state.unit);

  console.log('id',id)
  // Fetch unit when the component is mounted
  useEffect(() => {
    if (id) {
      dispatch(fetchUnitById(id)); 
    }

    // Cleanup function to reset unit state on unmount
  
  }, [dispatch, id]);


  // Use optional chaining and fallback to the default image if tower_photo is not available
  // const towerImage = selectedUnit?.tower_photo || user; // Fallback image if tower_photo is null or undefined
  const unitName = selectedUnit?.unit_name || "No unit name";
  const towerName = selectedUnit?.tower_name || "No tower name";
  const towerNumber = selectedUnit?.tower_number || "No tower number";
  const description = selectedUnit?.description || "No description available";

  const imageUrl = selectedUnit?.tower_photo
  ? `${api.defaults.baseURL}${selectedUnit.tower_photo}`
  : user;
  return (
    <div className="md:w-1/4 p-6 border-r border-gray-200">
      <div className=" mb-6">
        <h3 className="text-lg font-medium mb-2">Tower Image</h3>
        <img
          src={imageUrl}
          alt="Tower"
          className="rounded-lg  h-[254px] w-full object-cover"
        />
      </div>
      <div className="mb-4">
        <p className="text-sm text-gray-600">Unit Name</p>
        <p className="text-2xl font-semibold">{unitName}</p>
      </div>
      <div className="mb-4">
        <p className="text-sm text-gray-600">Tower Name</p>
        <p className="text-sm font-medium">{towerName}</p>
      </div>
      <div className="mb-4">
        <p className="text-sm text-gray-600">Tower Number</p>
        <p className="text-sm font-medium">{towerNumber}</p>
      </div>
      <div>
        <p className="text-sm text-gray-600 mb-2">About Us</p>
        <p className="text-sm font-medium">{description}</p>
      </div>
    </div>
  );
};

export default UnitTowerInfo;
