import React, { useState, useEffect, useRef } from "react";
import { FaFlag } from "react-icons/fa";
import { FaUserGroup } from "react-icons/fa6";
import { HiDotsHorizontal } from "react-icons/hi";
import { HiUserCircle } from "react-icons/hi";
import NoticeActionMenu from "../components/NoticeActionMenu";
import { PinIcon } from "../components/PinPost";
import UserCountDisplay from "../components/UserCountDisplay";
import LoadingAnimation from "../../../../Components/Loaders/LoadingAnimation";

// Priority color mapping - consistent with PriorityDropdown component
const PRIORITY_COLORS = {
  urgent: "text-red-500",
  high: "text-yellow-500",
  normal: "text-primary",
  low: "text-gray-400"
};

const getPriorityColor = (priority) => {
  return PRIORITY_COLORS[priority?.toLowerCase()] || "text-gray-500";
};

/**
 * NoticeListPreview Component
 * Renders the list of notices with their preview cards in a row-wise masonry layout
 */
const NoticeListPreview = ({
  // Data props
  notices,
  loading,

  // UI state props
  openDropdownId,
  dropdownRef,
  currentTab,

  // Handlers
  handleDropdownToggle,
  onEdit,
  onDelete,
  onExpire,
  onRestore,
  onPinToggle,
  onImageClick,
  onViewHistory,
  onReminder,
  handleDocumentClick,
  activeTab,

  // Utility functions
  isDocument,
  getFileIcon
}) => {
  const containerRef = useRef(null);
  const [positions, setPositions] = useState([]);
  const [containerHeight, setContainerHeight] = useState(0);
  const [isLayoutReady, setIsLayoutReady] = useState(false);

  // Two-pass layout: first render naturally, then measure and position
  useEffect(() => {
    if (!containerRef.current || notices.length === 0) {
      setPositions([]);
      setContainerHeight(0);
      setIsLayoutReady(false);
      return;
    }

    const calculateLayout = () => {
      const containerWidth = containerRef.current.offsetWidth;
      const cardWidth = 350; // Fixed card width
      const gap = 8; // 8px gap between cards

      // Calculate number of columns that fit
      const columns = Math.max(
        1,
        Math.floor((containerWidth + gap) / (cardWidth + gap))
      );
      const columnHeights = new Array(columns).fill(0);
      const newPositions = [];

      // Get all card elements to measure their actual heights
      const cardElements =
        containerRef.current.querySelectorAll("[data-card-id]");

      if (cardElements.length === notices.length) {
        // All cards are rendered, measure their heights
        notices.forEach((_, index) => {
          const cardElement = cardElements[index];
          const cardHeight = cardElement ? cardElement.offsetHeight : 300;

          // Find the shortest column for optimal space usage
          const shortestColumn = columnHeights.indexOf(
            Math.min(...columnHeights)
          );

          // Position the card
          newPositions.push({
            left: shortestColumn * (cardWidth + gap),
            top: columnHeights[shortestColumn]
          });

          // Update column height with actual card height + gap
          columnHeights[shortestColumn] += cardHeight + gap;
        });

        setPositions(newPositions);
        setContainerHeight(Math.max(...columnHeights) - gap);
        setIsLayoutReady(true);
      } else {
        // Cards not fully rendered yet, wait for next frame
        setIsLayoutReady(false);
        requestAnimationFrame(calculateLayout);
      }
    };

    // Start layout calculation
    setIsLayoutReady(false);
    const timeoutId = setTimeout(calculateLayout, 50);

    // Recalculate when content changes or window resizes
    const resizeObserver = new ResizeObserver(() => {
      calculateLayout();
    });

    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      clearTimeout(timeoutId);
      resizeObserver.disconnect();
    };
  }, [notices]);

  if (loading) {
    return (
      <div className="col-span-full flex justify-center items-center py-12">
        <LoadingAnimation />
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className="relative w-full"
      style={{
        height: isLayoutReady ? `${containerHeight}px` : "auto"
      }}
    >
      {notices.map((notice, index) => {
        const position = positions[index];

        return (
          <div
            key={notice.id}
            data-card-id={notice.id}
            className={`bg-white border border-primary rounded-lg p-4 shadow-sm ${
              isLayoutReady ? "absolute" : "relative mb-2"
            }`}
            style={{
              width: "350px",
              ...(isLayoutReady && position
                ? {
                    left: `${position.left}px`,
                    top: `${position.top}px`
                  }
                : {})
            }}
          >
            {/* Header */}
            <div className="flex justify-between items-start mb-3">
              <div className="flex items-center space-x-2">
                <div className="w-[24px] h-[24px] rounded-full flex items-center justify-center">
                  {notice.postAs === "creator" ? (
                    <HiUserCircle className="w-8 h-8" color="gray" />
                  ) : notice.postAs === "group" ? (
                    <FaUserGroup className="w-8 h-8" color="gray" />
                  ) : notice.postAs === "member" ? (
                    <FaUserGroup className="w-8 h-8" color="gray" />
                  ) : (
                    <FaUserGroup className="w-8 h-8" color="gray" />
                  )}
                </div>
                <div>
                  <h3 className="text-black text-[14px] font-bold">
                    {notice.author}
                  </h3>
                  <p className="text-black text-[11px] font-bold">
                    Creator {notice.creatorName}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {/* Pin Icon */}
                <PinIcon
                  notice={notice}
                  onPinIconClick={onPinToggle}
                  currentTab={currentTab}
                />
                {/* Priority Flag */}
                {notice.priority && (
                  <FaFlag
                    className={`w-[16px] h-[16px] ${getPriorityColor(
                      notice.priority
                    )}`}
                  />
                )}
                <UserCountDisplay notice={notice} />
                <div
                  className="relative"
                  ref={openDropdownId === notice.id ? dropdownRef : null}
                >
                  <HiDotsHorizontal
                    className="w-[16px] h-[16px] text-primary cursor-pointer hover:text-[#2A7A78]"
                    onClick={() => handleDropdownToggle(notice.id)}
                  />
                  {openDropdownId === notice.id && (
                    <NoticeActionMenu
                      notice={notice}
                      onEdit={onEdit}
                      onDelete={onDelete}
                      onExpire={onExpire}
                      onRestore={onRestore}
                      onViewHistory={onViewHistory}
                      onPinToggle={onPinToggle}
                      onReminder={onReminder}
                      activeTab={activeTab}
                      onClose={() => handleDropdownToggle(null)}
                    />
                  )}
                </div>
              </div>
            </div>

            {/* Date */}
            <div className="text-[11px] mb-2 flex items-center gap-4">
              <span className="text-primary font-bold whitespace-nowrap">
                Start:{" "}
                {(() => {
                  const date = new Date(notice.startDate);
                  const day = date.getDate().toString().padStart(2, "0");
                  const month = (date.getMonth() + 1)
                    .toString()
                    .padStart(2, "0");
                  const year = date.getFullYear();
                  const time = notice.startTime || "";
                  const [hours, minutes] = time.split(":");
                  const hour24 = parseInt(hours);
                  const hour12 =
                    hour24 === 0 ? 12 : hour24 > 12 ? hour24 - 12 : hour24;
                  const period = hour24 >= 12 ? "pm" : "am";
                  return `${day}-${month}-${year} at ${hour12}:${minutes}${period}`;
                })()}
              </span>
              <span className="text-error font-bold whitespace-nowrap">
                Expire:{" "}
                {(() => {
                  const date = new Date(notice.endDate);
                  const day = date.getDate().toString().padStart(2, "0");
                  const month = (date.getMonth() + 1)
                    .toString()
                    .padStart(2, "0");
                  const year = date.getFullYear();
                  const time = notice.endTime || "";
                  const [hours, minutes] = time.split(":");
                  const hour24 = parseInt(hours);
                  const hour12 =
                    hour24 === 0 ? 12 : hour24 > 12 ? hour24 - 12 : hour24;
                  const period = hour24 >= 12 ? "pm" : "am";
                  return `${day}-${month}-${year} at ${hour12}:${minutes}${period}`;
                })()}
              </span>
            </div>

            {/* Label - Next line */}
            {notice.label && (
              <div className="text-[12px] mb-2">
                <div className="flex flex-wrap gap-1">
                  {notice.label.split(",").map((label, index) => (
                    <span
                      key={index}
                      className="bg-[#F5F5F5] text-black text-[10px] px-2 py-1 rounded font-bold"
                    >
                      {label.trim()}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Title */}
            <h4 className="text-[#000] text-[14px] font-semibold mb-2 line-clamp-2">
              {notice.title}
            </h4>

            {/* Description */}
            <p className="text-[#666] text-[12px] mb-3">
              {notice.description}
            </p>

            {/* Attachments - Show only if they exist */}
            {notice.attachments &&
              notice.attachments.length > 0 && (
                <div className="mb-3">
                  <div className="space-y-2">
                    {(() => {
                      console.log(
                        `Notice ${notice.id} attachments:`,
                        notice.attachments
                      );
                      console.log(
                        `Attachment structure for notice ${notice.id}:`,
                        notice.attachments?.map((att) => ({
                          id: att.id,
                          file_url: att.file_url,
                          file_name: att.file_name,
                          url: att.url,
                          name: att.name
                        }))
                      );

                      const totalAttachments = notice.attachments.length;

                      return (
                        <div className="space-y-2">
                          {/* Main/First Image/PDF - Display prominently */}
                          <div
                            key={notice.attachments[0].id || 0}
                            className="relative bg-gray-100 overflow-hidden cursor-pointer hover:opacity-90 transition-all duration-200 border border-gray-200 w-[316px] h-[243px] rounded-[16px]"
                            onClick={() =>
                              isDocument(
                                notice.attachments[0].file_name ||
                                  notice.attachments[0].name
                              )
                                ? handleDocumentClick(
                                    notice.attachments[0]
                                  )
                                : onImageClick(
                                    notice.attachments[0],
                                    notice
                                  )
                            }
                          >
                            {isDocument(
                              notice.attachments[0].file_name ||
                                notice.attachments[0].name
                            ) ? (
                              <div className="w-full h-full flex items-center justify-center">
                                <div className="flex items-center">
                                  {getFileIcon(
                                    notice.attachments[0].file_name ||
                                      notice.attachments[0].name
                                  )}
                                  <div className="ml-2 text-left">
                                    <div className="text-sm font-medium text-gray-900">
                                      {(
                                        notice.attachments[0].file_name ||
                                        notice.attachments[0].name
                                      )
                                        ?.toLowerCase()
                                        .endsWith(".pdf")
                                        ? "PDF Document"
                                        : "Word Document"}
                                    </div>
                                    <div className="text-xs text-gray-500">
                                      {notice.attachments[0].file_name ||
                                        notice.attachments[0].name}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            ) : (
                              <img
                                src={
                                  notice.attachments[0].file_url ||
                                  notice.attachments[0].url ||
                                  notice.attachments[0]
                                }
                                alt={
                                  notice.attachments[0].file_name ||
                                  notice.attachments[0].name ||
                                  `Attachment 1`
                                }
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  console.error(
                                    "Image load error for:",
                                    notice.attachments[0].file_url
                                  );
                                  e.target.style.display = "none";
                                }}
                              />
                            )}
                          </div>

                          {/* Additional Images/Files - Show as thumbnails if more than 1 */}
                          {totalAttachments > 1 && (
                            <div className="flex gap-2 overflow-x-auto">
                              {notice.attachments
                                .slice(1)
                                .map((attachment, index) => (
                                  <div
                                    key={attachment.id || index + 1}
                                    className="relative flex-shrink-0 bg-gray-100 overflow-hidden cursor-pointer hover:opacity-90 transition-all duration-200 border border-gray-200"
                                    style={{
                                      width: "75px",
                                      height: "57.1px",
                                      borderRadius: "16px"
                                    }}
                                    onClick={() =>
                                      isDocument(
                                        attachment.file_name || attachment.name
                                      )
                                        ? handleDocumentClick(attachment)
                                        : onImageClick(
                                            attachment,
                                            notice
                                          )
                                    }
                                  >
                                    {isDocument(
                                      attachment.file_name || attachment.name
                                    ) ? (
                                      <div className="w-full h-full flex items-center justify-center">
                                        <div className="scale-75">
                                          {getFileIcon(
                                            attachment.file_name ||
                                              attachment.name
                                          )}
                                        </div>
                                      </div>
                                    ) : (
                                      <img
                                        src={
                                          attachment.file_url ||
                                          attachment.url ||
                                          attachment
                                        }
                                        alt={
                                          attachment.file_name ||
                                          attachment.name ||
                                          `Attachment ${index + 2}`
                                        }
                                        className="w-full h-full object-cover"
                                        onError={(e) => {
                                          console.error(
                                            "Image load error for:",
                                            attachment.file_url
                                          );
                                          e.target.style.display = "none";
                                        }}
                                      />
                                    )}
                                  </div>
                                ))}
                            </div>
                          )}
                        </div>
                      );
                    })()}
                  </div>
                </div>
              )}
          </div>
        );
      })}
    </div>
  );
};

export default NoticeListPreview;
