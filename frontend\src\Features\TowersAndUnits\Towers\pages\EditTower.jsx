import { useEffect, useState, useRef, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Paragraph } from "../../../../Components/Ui/Paragraph";
import { Heading } from "../../../../Components/Ui/Heading";
import { GoPlus, GoTrash } from "react-icons/go";
import ArrowHeading from "../../../../Components/HeadingComponent/ArrowHeading";
import TextInputComponent from "../../../../Components/FormComponent/TextInputComponent";
import NumberInputComponent from "../../../../Components/FormComponent/NumberInputComponent";
import TextareaComponent from "../../../../Components/FormComponent/TextareaComponent";
import CheckboxComponent from "../../../../Components/FormComponent/CheckboxComponent";
import RadioComponent from "../../../../Components/FormComponent/RadioComponent";
import DynamicTowerTable from "../components/DynamicTowerTable";
import MessageBox from "../../../../Components/MessageBox/MessageBox";
import SubmitButton from "../../../../Components/FormComponent/ButtonComponent/SubmitButton";
import img from "../../../../../public/image_upload.png";
import {
  clearMessages,
  fetchTowerById,
  updateTower,
  resetTowers
} from "../../../../redux/slices/towers/towerSlice";
import { checkPermission } from "../../../../utils/permissionUtils";

const schema = yup.object().shape({
  tower_name: yup
    .string()
    .max(50, "Tower name cannot exceed 50 characters.")
    .required("Tower name is required."),

  num_floors: yup
    .number()
    .typeError("Number of floors must be a number")
    .required("Number of floors is required.")
    .min(1, "Number of floors must be greater than 0") // Ensures positive number
    .max(50, "Number of floors cannot exceed 100"), // Ensures maximum of 50

  num_units: yup
    .number()
    .typeError("Units per floor must be a number")
    .required("Units per floor are required.")
    .min(1, "Units per floor must be greater than 0") // Ensures positive number
    .max(26, "Units per floor cannot exceed 26"), // Ensures maximum of 26

  unit_naming_type: yup.string().required("Unit naming is required."),
  add_tower_number_to_unit_name: yup.boolean(),
  units_per_floor: yup.string().required("Unit type is required."),

  number_of_units: yup
    .array()
    .of(
      yup
        .number()
        .typeError("Number of units must be a number")
        .min(1, "Units must be greater than 0")
    )
    .required(
      "Unit distribution is required when using 'Different' configuration"
    ),

  description: yup
    .string()
    .transform((value, originalValue) => {
      return originalValue.trim() === "" ? undefined : value;
    })
    .test("wordCount", "Description cannot exceed 100 words", (value) => {
      if (!value) return true;
      const wordCount = value.trim().split(/\s+/).length;
      return wordCount <= 100;
    }),
  photo: yup
    .mixed()
    .nullable()
    .test("fileSize", "File size must be less than 5MB", (value) => {
      if (!value || typeof value === "string") return true;
      return value.size <= 5 * 1024 * 1024;
    })
    .test("fileType", "Unsupported file format", (value) => {
      if (!value || typeof value === "string") return true;
      return ["image/jpeg", "image/png"].includes(value.type);
    })
});

const EditTower = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { id } = useParams(); // Get ID from URL params

  const { singleTower, loading, error, successMessage } = useSelector(
    (state) => state.tower || {}
  );

  const [imagePreview, setImagePreview] = useState(null);
  const [fileError, setFileError] = useState("");
  const [isRemoved, setIsRemoved] = useState(false);
  const [newImageUploaded, setNewImageUploaded] = useState(false);
  const [hasPermission, setHasPermission] = useState(false);
  const [loadingPermission, setLoadingPermission] = useState(true);
  const [floorEditErrors, setFloorEditErrors] = useState([]);

  const {
    register,
    handleSubmit,
    setValue,
    setError,
    clearErrors,
    watch,
    reset,
    formState: { errors: formErrors, isDirty }
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      tower_name: "",
      description: "",
      num_floors: "",
      num_units: "",
      num_floors_edit: "",
      num_units_edit: "",
      unit_naming_type: "Numerical",
      add_tower_number_to_unit_name: false,
      units_per_floor: "Same as Every Floor"
    }
  });

  const [unitsPerFloor, setUnitsPerFloor] = useState("Same as Every Floor");
  const [floorEdits, setFloorEdits] = useState([]);
  const numFloors = watch("num_floors") || 0;
  const numUnits = watch("num_units") || 0;
  useEffect(() => {
    const fetchPermission = async () => {
      const permissionGranted = await checkPermission("org", 11);
      setHasPermission(permissionGranted);
      setLoadingPermission(false);
    };
    fetchPermission();
  }, []);

  useEffect(() => {
    if (
      error === "This tower has been deleted." ||
      error === "Tower not found."
    ) {
      // Do nothing here, let the MessageBox handle it
    }
  }, [error]);

  useEffect(() => {
    if (id) {
      dispatch(fetchTowerById(id));
    }

    return () => {
      dispatch(resetTowers());
    };
  }, [dispatch, id]);

  useEffect(() => {
    if (singleTower) {
      setValue("tower_name", singleTower.tower_name || "");
      setValue("tower_number", singleTower.tower_number || null);
      setValue("description", singleTower.description || "");
      setValue("num_floors", singleTower.num_floors || null);
      setValue("num_units", singleTower.num_units || null);
      setValue("unit_naming_type", singleTower.unit_naming_type || "Numerical");
      setValue(
        "units_per_floor",
        singleTower.units_per_floor || "Same as Every Floor"
      );

      setValue(
        "add_tower_number_to_unit_name",
        singleTower.add_tower_number_to_unit_name || false
      );

      console.log(singleTower);

      if (singleTower.number_of_units) {
        setValue("number_of_units", singleTower.number_of_units);
      }

      if (singleTower?.photo) {
        setValue("photo", singleTower.photo);
        setImagePreview(`http://127.0.0.1:8000/${singleTower.photo}`);
      } else {
        setValue("photo", null);
        setImagePreview(null);
      }

      if (singleTower.floors && Array.isArray(singleTower.floors)) {
        setValue("floors", singleTower.floors);
      } else {
        setValue("floors", []);
      }

      const allUnits =
        singleTower.floors?.flatMap((floor) => floor.units) || [];
      setValue("units", allUnits);

      if (singleTower) {
        const towerUnitsPerFloor =
          singleTower.units_per_floor || "Same as Every Floor";
        setUnitsPerFloor(towerUnitsPerFloor);
        setValue("units_per_floor", towerUnitsPerFloor);

        if (towerUnitsPerFloor === "Different" && singleTower.floors) {
          const floorEditsData = singleTower.floors.map((floor) => ({
            floor: floor.floor_no,
            units: floor.number_of_units
          }));
          setFloorEdits(floorEditsData);
          setPreviousFloorEdits(floorEditsData); // Store initial state
        }
      }
    }
  }, [singleTower, setValue]);

  // useEffect to update preview if server image exists and new image is not uploaded
  useEffect(() => {
    if (singleTower && singleTower.photo && !isRemoved && !newImageUploaded) {
      setImagePreview(`http://127.0.0.1:8000/${singleTower.photo}`);
    }
  }, [singleTower, isRemoved, newImageUploaded]);

  // Handle new image upload
  const handleFileChange = (event) => {
    const file = event.target.files[0];

    if (!file) {
      // ✅ If no file is selected, mark photo as removed
      setValue("photo", "", { shouldDirty: true });
      setImagePreview(null);
      setIsRemoved(true);
      setFileError(""); // Clear previous error
      clearErrors("photo");
      return;
    }

    setValue("photo", file, { shouldDirty: true }); // ✅ Mark as dirty

    // ✅ File size validation (Max: 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setFileError("File size must be less than 5MB");
      setError("photo", {
        type: "manual",
        message: "File size must be less than 5MB"
      });
      return;
    }

    // ✅ File format validation
    if (!["image/jpeg", "image/png"].includes(file.type)) {
      setFileError(
        "Unsupported file format. Only JPG, JPEG, and PNG are allowed."
      );
      setError("photo", {
        type: "manual",
        message: "Unsupported file format. Only JPG, JPEG, and PNG are allowed."
      });
      return;
    }

    // ✅ Clear errors if file is valid
    setFileError("");
    clearErrors("photo");

    // ✅ Read and preview the image
    const reader = new FileReader();
    reader.onloadend = () => {
      setImagePreview(reader.result);
      setNewImageUploaded(true);
      setIsRemoved(false);
    };
    reader.readAsDataURL(file);
  };

  // Handle remove image
  const handleRemoveImage = () => {
    setImagePreview("");
    // setValue("photo", "");
    setValue("photo", "", { shouldDirty: true });

    // setImagePreview(null);
    // setValue("photo", null);
    setFileError("");
    clearErrors("photo");
    document.getElementById("file-upload").value = "";
    setNewImageUploaded(false);
    setIsRemoved(true);
  };

  // Update `number_of_units` dynamically
  useEffect(() => {
    let updatedUnits = new Array(numFloors).fill(numUnits);
    floorEdits.forEach((edit) => {
      if (edit.floor >= 1 && edit.floor <= numFloors) {
        updatedUnits[edit.floor - 1] = edit.units || numUnits;
      }
    });
    setValue("number_of_units", updatedUnits);
  }, [numFloors, numUnits, floorEdits, setValue]);

  const [previousFloorEdits, setPreviousFloorEdits] = useState([]); // Store previous "Different" config

  useEffect(() => {
    if (singleTower) {
      setValue(
        "units_per_floor",
        singleTower.units_per_floor || "Same as Every Floor"
      );

      if (singleTower.units_per_floor === "Different") {
        const floorEditsData = singleTower.floors.map((floor) => ({
          floor: floor.floor_no,
          units: floor.number_of_units
        }));
        setFloorEdits(floorEditsData);
        setPreviousFloorEdits(floorEditsData); // Store the initial state
      }
    }
  }, [singleTower, setValue]);

  const handleUnitTypeChange = (value) => {
    setUnitsPerFloor(value);
    setValue("units_per_floor", value, { shouldDirty: true });

    if (value === "Same as Every Floor") {
      setPreviousFloorEdits(floorEdits);
      setFloorEdits([]);
      setValue("number_of_units", new Array(numFloors).fill(numUnits), {
        shouldDirty: true
      });
    } else if (value === "Different") {
      setFloorEdits(previousFloorEdits);
      setValue(
        "number_of_units",
        previousFloorEdits.map((edit) => edit.units),
        { shouldDirty: true }
      );
    }
  };

  // **Handle adding a new floor edit**
  // const addFloorEdit = () => {
  //   setFloorEdits((prevEdits) => {
  //     // Prevent duplicate floor entries
  //     const floorNumbers = prevEdits.map((edit) => edit.floor);

  //     if (floorNumbers.includes("")) {
  //       return prevEdits; // Prevent adding multiple empty floors
  //     }

  //     return [...prevEdits, { floor: "", units: "" }];
  //   });
  // };

  const [maxFloorReached, setMaxFloorReached] = useState(false);

  const addFloorEdit = () => {
    if (floorEdits.length >= numFloors) {
      setMaxFloorReached(true);
      return; // Prevent adding more floors
    }
    setMaxFloorReached(false);

    setFloorEdits((prevEdits) => [...prevEdits, { floor: "", units: "" }]);
  };

  const updateFloorEdit = (index, field, value) => {
    setFloorEdits((prevEdits) => {
      const updatedEdits = [...prevEdits];

      let updatedErrors = [...floorEditErrors];

      if (field === "floor") {
        if (value === "" || isNaN(value)) {
          updatedErrors[index] = {
            ...updatedErrors[index],
            floor: "Floor number is required."
          };
          setFloorEditErrors(updatedErrors);
          return prevEdits; // Prevent updating with empty value
        }

        if (value < 1 || value > numFloors) {
          updatedErrors[index] = {
            ...updatedErrors[index],
            floor: `Floor number must be between 1 and ${numFloors}.`
          };
          setFloorEditErrors(updatedErrors);
          return prevEdits;
        }

        // Check if floor already exists
        const floorExists = updatedEdits.some(
          (edit, i) => edit.floor === value && i !== index
        );

        if (floorExists) {
          updatedErrors[index] = {
            ...updatedErrors[index],
            floor:
              "This floor is already assigned. Please choose another floor."
          };
          setFloorEditErrors(updatedErrors);
          return prevEdits;
        } else {
          updatedErrors[index] = { ...updatedErrors[index], floor: "" }; // Clear error if valid
        }
      }

      if (field === "units") {
        if (value === "" || isNaN(value)) {
          updatedErrors[index] = {
            ...updatedErrors[index],
            units: "Units per floor is required."
          };
          setFloorEditErrors(updatedErrors);
          return prevEdits;
        }

        if (value < 1 || value > 26) {
          updatedErrors[index] = {
            ...updatedErrors[index],
            units: "Units must be between 1 and 26."
          };
          setFloorEditErrors(updatedErrors);
          return prevEdits;
        } else {
          updatedErrors[index] = { ...updatedErrors[index], units: "" }; // Clear error if valid
        }
      }

      setFloorEditErrors(updatedErrors);
      updatedEdits[index][field] = value;
      return updatedEdits;
    });

    let updatedUnits = new Array(numFloors).fill(numUnits);
    floorEdits.forEach((edit) => {
      if (edit.floor >= 1 && edit.floor <= numFloors) {
        updatedUnits[edit.floor - 1] = edit.units || numUnits;
      }
    });

    setValue("number_of_units", updatedUnits);
  };

  // **Handle removing an edited floor**
  const removeFloorEdit = (index) => {
    const updatedEdits = floorEdits.filter((_, i) => i !== index);
    setFloorEdits(updatedEdits);

    let updatedUnits = new Array(numFloors).fill(numUnits);
    updatedEdits.forEach((edit) => {
      if (edit.floor >= 1 && edit.floor <= numFloors) {
        updatedUnits[edit.floor - 1] = edit.units || numUnits;
      }
    });

    setValue("number_of_units", updatedUnits);
  };

  const onSubmit = async (data) => {
    try {
      const formData = new FormData();
      const allowedImageTypes = [
        "image/jpeg",
        "image/png",
        "image/jpg",
        "image/webp"
      ];

      Object.keys(data).forEach((key) => {
        const value = data[key];

        if (["photo"].includes(key)) {
          if (typeof value === "string" && value.startsWith("/media/")) {
            console.log(`Skipping ${key} URL:`, value); // Don't append if it's a URL
            return;
          } else if (value instanceof File || value instanceof Blob) {
            // ✅ Validate file type if it is a file
            if (!allowedImageTypes.includes(value.type)) {
              return; // Stop form submission if the file format is not correct
            }
            formData.append(key, value); // Append the valid file
          } else if (value === "") {
            formData.append(`${key}_removed`, "Removed"); // Mark as removed if empty
          }
        } else if (Array.isArray(value)) {
          // If it's an array, append each value separately
          value.forEach((item) => {
            formData.append(key, item);
          });
        } else if (typeof value === "boolean" || typeof value === "number") {
          // Convert Boolean & Number to String
          formData.append(key, String(value));
        } else if (typeof value === "object" && value !== null) {
          // If it's an object, convert it to JSON string
          formData.append(key, JSON.stringify(value));
        } else {
          // Otherwise, append the value directly
          formData.append(key, value || "");
        }
      });

      console.log(
        "Form data submitted:",
        Object.fromEntries(formData.entries())
      );

      const result = await dispatch(updateTower({ formData, id }));

      if (updateTower.fulfilled.match(result)) {
        console.log("Update Successful:", result.payload);
        reset(); // ✅ Clears the form fields after successful submission
      } else {
        console.error("Update Failed:", result.payload);
      }
    } catch (error) {
      console.error("Error submitting update:", error);
    }
  };

  if (loadingPermission) {
    return <div></div>;
  }

  if (!hasPermission) {
    navigate("/not-authorized");
    return null;
  }

  return (
    <div>
      <div className="h-full">
        <div className="container">
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="bg-white pt-2 mx-auto px-5">
              <div className="md:flex justify-between py-2">
                <ArrowHeading
                  title={"Edit Tower"}
                  size="xl"
                  color="black"
                  onNext={() => navigate(-1)}
                  fontWeight="semibold"
                />
              </div>
              <div className="md:flex md:gap-6">
                <div className="w-[60%] px-5">
                  <Heading level={1} className="my-2 text-sm font-medium">
                    Tower & Unit Information
                  </Heading>
                  <div className="flex gap-4">
                    <div className="w-1/2">
                      <Paragraph className="my-3 text-sm font-medium">
                        Tower Name*
                      </Paragraph>
                      <TextInputComponent
                        name="tower_name"
                        placeholder="Enter Tower Name"
                        value={watch("tower_name") || ""}
                        onChange={(e) =>
                          setValue("tower_name", e.target.value, {
                            shouldDirty: true
                          })
                        }
                        error={formErrors.tower_name?.message}
                      />
                    </div>
                    <div className="w-1/2">
                      <Paragraph className="my-3 text-sm font-medium">
                        Tower Number
                      </Paragraph>
                      <NumberInputComponent
                        name="tower_number"
                        placeholder="Enter Tower Number"
                        value={watch("tower_number") || ""}
                        onChange={(e) =>
                          setValue("tower_number", Number(e.target.value), {
                            shouldDirty: true
                          })
                        }
                        error={formErrors.tower_number?.message}
                        disabled={true} // This will disable the input field
                      />
                    </div>
                  </div>
                  <div className="w-full mt-4">
                    <Paragraph className="my-3 text-sm font-medium">
                      Description
                    </Paragraph>
                    <div className="login-field">
                      <textarea
                        name="description"
                        rows={4}
                        placeholder="Enter your description here"
                        {...register("description", {
                          required: "Description is required"
                        })}
                        className={`login-field-input ${
                          formErrors.description ? "border-red-500" : ""
                        }`}
                      />
                      {formErrors.description && (
                        <span className="text-red-500 text-xs mt-1 block">
                          {formErrors.description.message}
                        </span>
                      )}
                    </div>
                  </div>
                  <Paragraph className="my-3 text-sm font-medium">
                    Photo
                  </Paragraph>
                  <div className="relative w-1/2 pt-5 pb-5 rounded-27 profile-picture flex flex-col items-center border border-dashed">
                    <label
                      htmlFor="file-upload"
                      className="relative cursor-pointer w-24 h-24 flex items-center justify-center overflow-hidden"
                    >
                      {imagePreview ? (
                        <img
                          src={imagePreview}
                          alt="Preview"
                          className="object-cover w-full h-full rounded-md"
                        />
                      ) : singleTower?.photo && !isRemoved ? (
                        <img
                          src={`http://127.0.0.1:8000/${singleTower.photo}`}
                          alt="Current Tower Photo"
                          className="object-cover w-full h-full rounded-md"
                        />
                      ) : (
                        <img
                          src={img}
                          alt="Upload Placeholder"
                          className="m-3 w-[45px] h-[45px]"
                        />
                      )}
                    </label>
                    <input
                      id="file-upload"
                      name="photo"
                      type="file"
                      accept=".jpg,.jpeg,.png"
                      className="hidden"
                      onChange={handleFileChange}
                    />
                    {(imagePreview || (singleTower?.photo && !isRemoved)) && (
                      <button
                        type="button"
                        onClick={handleRemoveImage}
                        className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center shadow-lg hover:bg-red-600"
                        aria-label="Remove Image"
                      >
                        &#10006;
                      </button>
                    )}
                    {!(imagePreview || (singleTower?.photo && !isRemoved)) && (
                      <p>Upload Tower Photo</p>
                    )}
                    {/* {fileError && (
                                        <p className="text-red-500 text-sm">{fileError}</p>
                                      )}
                                      {formErrors?.photo && (
                                        <p className="text-red-500 text-sm">
                                          {formErrors.photo.message}
                                        </p>
                                      )} */}
                    {(fileError || formErrors?.photo) && (
                      <p className="text-red-500 text-sm">
                        {fileError || formErrors.photo?.message}
                      </p>
                    )}
                  </div>

                  <div className="flex gap-4 mt-4">
                    <div className="w-1/2">
                      <Paragraph className="my-3 text-sm font-medium">
                        Number of Floors*
                      </Paragraph>
                      <NumberInputComponent
                        name="num_floors"
                        placeholder="Enter number of floors"
                        value={watch("num_floors") || ""}
                        onChange={(e) => {
                          let value = parseInt(e.target.value) || 1;
                          value = Math.min(50, Math.max(1, value)); // ✅ Prevents over 100
                          setValue("num_floors", value, { shouldDirty: true });
                          clearErrors("num_floors"); // ✅ Clears validation error when valid input is entered
                        }}
                        error={formErrors.num_floors?.message}
                      />
                    </div>
                    <div className="w-1/2">
                      <Paragraph className="my-3 text-sm font-medium">
                        Units in Each Floor*
                      </Paragraph>
                      <NumberInputComponent
                        name="num_units"
                        placeholder="Enter units per floor"
                        min="1"
                        max="26" // Restricts user input
                        value={watch("num_units") || ""}
                        onChange={(e) => {
                          let value = parseInt(e.target.value) || 1;
                          value = Math.min(26, Math.max(1, value)); // ✅ Prevents over 26
                          setValue("num_units", Number(value), {
                            shouldDirty: true
                          });
                          clearErrors("num_units"); // ✅ Clears validation error when valid input is entered
                        }}
                        error={formErrors.num_units?.message}
                      />
                    </div>
                  </div>
                  <div className="flex item-center justify-between gap-2 mt-2">
                    <div className="w-1/2">
                      <Paragraph className="my-3 text-sm font-medium">
                        Unit Naming*
                      </Paragraph>
                      <RadioComponent
                        options={[
                          { label: "Numerical", value: "Numerical" },
                          { label: "Alphabetical", value: "Alphabetical" }
                        ]}
                        selectedValue={watch("unit_naming_type")}
                        onChange={(e) =>
                          setValue("unit_naming_type", e.target.value, {
                            shouldDirty: true
                          })
                        }
                        name="unit_naming_type"
                      />
                    </div>
                    <div className="w-1/2 mt-12">
                      <CheckboxComponent
                        label="Add Tower Number to Unit Name"
                        checked={watch("add_tower_number_to_unit_name")}
                        onChange={(e) =>
                          setValue(
                            "add_tower_number_to_unit_name",
                            e.target.checked,
                            { shouldDirty: true }
                          )
                        }
                        name="add_tower_number_to_unit_name"
                      />
                    </div>
                  </div>
                </div>
                <div className="w-[40%] px-5 md:border-l-[#F9F9FB] md:border-l-[4px]">
                  <div className="w-full pl-5">
                    <Paragraph className="my-3 text-sm font-medium">
                      Unit*
                    </Paragraph>
                    <RadioComponent
                      options={[
                        {
                          label: "Same as Every Floor",
                          value: "Same as Every Floor"
                        },
                        { label: "Different", value: "Different" }
                      ]}
                      // selectedValue={unitsPerFloor}
                      selectedValue={watch("units_per_floor")}
                      onChange={(e) => handleUnitTypeChange(e.target.value)}
                      name="units_per_floor"
                    />
                  </div>
                  {unitsPerFloor === "Different" && (
                    <>
                      <div className="flex flex-col gap-4 mt-4 pl-5">
                        <button
                          type="button"
                          onClick={() => {
                            addFloorEdit();
                            setValue(
                              "floors",
                              [...floorEdits, { floor: "", units: "" }],
                              { shouldDirty: true }
                            );
                          }}
                          className="flex justify-center items-center w-10 h-10 rounded-full bg-[#ECF5F5] hover:bg-gray-300 transition-all"
                          aria-label="Add Floor Unit"
                        >
                          <GoPlus className="text-black text-xl" />
                        </button>
                        {maxFloorReached && (
                          <p className="text-red-500 text-sm">
                            Maximum floor limit reached
                          </p>
                        )}

                        {floorEdits.map((edit, index) => (
                          <div key={index} className="flex flex-col gap-2">
                            <div className="flex items-center gap-4 mb-2">
                              {/* Floor Number Input */}
                              <div>
                                <NumberInputComponent
                                  name={`floor_edit_${index}`}
                                  placeholder="Floor Number"
                                  value={edit.floor}
                                  onChange={(e) => {
                                    let value =
                                      e.target.value.trim() === ""
                                        ? ""
                                        : Number(e.target.value);
                                    updateFloorEdit(index, "floor", value);
                                    setValue("floors", [...floorEdits], {
                                      shouldDirty: true
                                    });
                                    clearErrors(`floor_edit_${index}`); // ✅ Clears validation error when valid input is entered
                                  }}
                                />
                                {floorEditErrors[index]?.floor && (
                                  <p className="text-red-500 text-sm">
                                    {floorEditErrors[index].floor}
                                  </p>
                                )}
                              </div>

                              {/* Units Input */}
                              <div>
                                <NumberInputComponent
                                  name={`units_edit_${index}`}
                                  placeholder="Units on Floor"
                                  value={edit.units}
                                  onChange={(e) => {
                                    let value =
                                      e.target.value.trim() === ""
                                        ? ""
                                        : Number(e.target.value);
                                    updateFloorEdit(index, "units", value);
                                    setValue("floors", [...floorEdits], {
                                      shouldDirty: true
                                    });
                                    clearErrors(`units_edit_${index}`); // ✅ Clears validation error when valid input is entered
                                  }}
                                />
                                {floorEditErrors[index]?.units && (
                                  <p className="text-red-500 text-sm">
                                    {floorEditErrors[index].units}
                                  </p>
                                )}
                              </div>

                              {/* Remove Button */}
                              <button
                                type="button"
                                onClick={() => {
                                  removeFloorEdit(index);
                                  setValue("floors", [...floorEdits], {
                                    shouldDirty: true
                                  });
                                }}
                                className="flex justify-center items-center w-10 h-10 rounded-full bg-[#ECF5F5] hover:bg-gray-300 transition-all"
                                aria-label="Remove Floor Unit"
                              >
                                <GoTrash className="text-black text-xl" />
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </>
                  )}

                  <div>
                    <DynamicTowerTable
                      num_floors={watch("num_floors") || 0}
                      num_units={watch("num_units") || 0}
                      unit_naming_type={
                        watch("unit_naming_type") || "Numerical"
                      }
                      add_tower_number_to_unit_name={
                        watch("add_tower_number_to_unit_name") || false
                      }
                      tower_number={watch("tower_number") || 1}
                      units_per_floor={
                        watch("units_per_floor") || "Same as Every Floor"
                      }
                      floorEdits={floorEdits} // Ensure updated floor edits are passed
                    />
                  </div>
                </div>
              </div>
              <div className="w-[50%] mx-auto mt-10 pb-10">
                <SubmitButton text="Save" width="full" disabled={!isDirty} />
              </div>
            </div>
          </form>
        </div>
        {/* Uncomment MessageBox if needed */}
        <MessageBox
          message={successMessage || error} // Show success or error message
          error={error}
          clearMessage={() => dispatch(clearMessages())}
          onOk={() => {
            dispatch(clearMessages());

            // Redirect to ViewTowers if the error is related to deletion
            if (
              error === "This tower has been deleted." ||
              error === "Tower not found."
            ) {
              navigate("/ViewTowers");
            } else if (successMessage) {
              navigate(-1); // Navigate back for other success cases
            }
          }}
        />
      </div>
    </div>
  );
};

export default EditTower;
