import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import TextInputComponent from "../../../Components/FormComponent/TextInputComponent";
import TextareaComponent from "../../../Components/FormComponent/TextareaComponent";
import {
  fetchPermissions,
  createRole,
  clearMessages
} from "../../../redux/slices/roles/rolesSlice";
import SubmitButton from "../../../Components/FormComponent/ButtonComponent/SubmitButton";
import MessageBox from "../../../Components/MessageBox/MessageBox";
import { checkPermission } from "../../../utils/permissionUtils";
import ErrorMessage from "../../../Components/MessageBox/ErrorMessage";

const AddMemberRole = ({ onRoleCreated, onClose }) => {
  const dispatch = useDispatch();
  const { permissions, loading } = useSelector((state) => state.role);

  const [formData, setFormData] = useState({
    role_name: "",
    role_description: "",
    permissions: []
  });
  const [errors, setErrors] = useState({
    role_name: "",
    role_description: ""
  });
  const [hasPermission, setHasPermission] = useState(false);
  const [loadingPermission, setLoadingPermission] = useState(true);
  // Local success state to show message box on successful creation
  const [localSuccess, setLocalSuccess] = useState(null);

  useEffect(() => {
    dispatch(clearMessages());
  }, [dispatch]);

  useEffect(() => {
    const fetchPermission = async () => {
      const permissionId = 4;
      const permissionGranted = await checkPermission("org", permissionId);
      setHasPermission(permissionGranted);
      setLoadingPermission(false);
    };
    fetchPermission();
    dispatch(fetchPermissions());
  }, [dispatch]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    setErrors((prev) => ({ ...prev, [name]: "" }));
  };

  const handleCheckboxChange = (e, permissionId) => {
    if (e.target.checked) {
      setFormData((prev) => ({
        ...prev,
        permissions: [...prev.permissions, permissionId]
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        permissions: prev.permissions.filter((id) => id !== permissionId)
      }));
    }
  };

  const handleSelectAllChange = (e) => {
    if (e.target.checked) {
      setFormData((prev) => ({
        ...prev,
        permissions: permissions.map((perm) => perm.id)
      }));
    } else {
      setFormData((prev) => ({ ...prev, permissions: [] }));
    }
  };

  const handleSubmitRole = (e) => {
    e.preventDefault();
    setErrors({ role_name: "", role_description: "" });

    dispatch(createRole(formData))
      .unwrap()
      .then((newRole) => {
        // Set a local success message along with the new role data.
        setLocalSuccess({ message: "Role created successfully", newRole });
      })
      .catch((err) => {
        setErrors({
          role_name: err.role_name ? err.role_name[0] : "",
          role_description: err.role_description ? err.role_description[0] : ""
        });
      });
  };

  if (loadingPermission) return <div></div>;
  if (!hasPermission) return <div>Not Authorized</div>;

  return (
  <div className="">
      <div className="container px-0">
        <div className="">
          <div className="bg-white border px-[14px] border-white   mx-auto">
            <form onSubmit={handleSubmitRole}>
              <div className="flex justify-between items-center">
                <div className="md:flex justify-between ">
                  <h2 className="flex items-center justify-between font-medium text-[24px]">
                    Add Role
                  </h2>
                </div>
              </div>
              <div className="">
                <div className="w-full">
               
                  <TextInputComponent
                    name="role_name"
                    label="Role Name"
                    placeholder="Write Role Name"
                    value={formData.role_name}
                    onChange={handleChange}
                    // error={errors.role_name}
                  />
                  {errors.role_name && (
                    <ErrorMessage
                      message={
                        errors.role_name.message || "Role name is required"
                      }
                    />
                  )}

                  <div className="w-full">
                    
                    <TextareaComponent
                                        label="Role Description"

                      name="role_description"
                      value={formData.role_description}
                      onChange={handleChange}
                      placeholder="Write Role Description"
                      rows={3}
                      error={errors.role_description}
                    />
                  </div>
                </div>
              </div>
              <div className="">
                <div>
                  <h1 className="text-[20px] font-semibold my-6">
                    Role Permission
                  </h1>
                  <div className="grid grid-cols-4 gap-4 w-full h-[400px] overflow-scroll">
                    <div>
                      <div className="mb-[8px]">
                        <h2 className="text-[16px] font-semibold p-[10px] text-center bg-[#3C9D9B1A] rounded-[8px]">
                          Member Management
                        </h2>
                      </div>
                      {/* <div className="flex flex-col p-4 rounded-[10px] bg-white border shadow overflow-sroll"> */}
                      <div className="flex flex-col p-4 rounded-[10px] bg-white border shadow w-[250px] h-[280px] overflow-scroll">
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            className="form-checkbox h-4 w-4 text-indigo-600 accent-[#3C9D9B]"
                            onChange={handleSelectAllChange}
                            checked={
                              permissions.length > 0 &&
                              formData.permissions.length === permissions.length
                            }
                          />
                          <label className="ml-2 mb-[3px]">Select All</label>
                        </div>
                        {permissions &&
                          permissions.map((perm) => (
                            <div key={perm.id} className="flex items-center">
                              <input
                                type="checkbox"
                                className="form-checkbox h-4 w-4 text-indigo-600 accent-[#3C9D9B]"
                                onChange={(e) =>
                                  handleCheckboxChange(e, perm.id)
                                }
                                checked={formData.permissions.includes(perm.id)}
                              />
                              <label className="ml-2 mb-[3px]">
                                {perm.permission_name}
                              </label>
                            </div>
                          ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="my-3 w-[80%] text-center mx-auto">
                <SubmitButton
                  text="Create Role"
                  loading={loading}
                  disabled={loading}
                  onClick={handleSubmitRole}
                  width="full"
                />
              </div>
            </form>
            {/* Show MessageBox with success message */}
            {localSuccess && (
              <MessageBox
                message={localSuccess.message}
                onOk={() => {
                  // alert()
                  if (onRoleCreated) onRoleCreated(localSuccess.newRole);
                  setLocalSuccess(null);
                  onClose();
                }}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddMemberRole;
